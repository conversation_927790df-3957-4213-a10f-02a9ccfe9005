{"name": "kryptopesa-admin-dashboard", "version": "1.0.0", "description": "Admin Dashboard for KryptoPesa P2P Trading Platform", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@mui/x-charts": "^6.0.0-alpha.18", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "axios": "^1.4.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "notistack": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-query": "^3.39.3", "react-router-dom": "^6.14.2", "react-scripts": "^5.0.1", "recharts": "^2.7.2", "socket.io-client": "^4.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "eslint": "^8.45.0", "prettier": "^3.0.0"}, "proxy": "http://localhost:3000"}