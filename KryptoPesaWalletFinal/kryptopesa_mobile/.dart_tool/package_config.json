{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "_macros", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/dart-sdk/pkg/_macros", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "asn1lib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "bip39", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bip39-1.0.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dio", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ed25519_hd_key", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_hd_key-2.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "eip1559", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/eip1559-0.6.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "eip55", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/eip55-1.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "encrypt", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.17.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_messaging_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.8.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_cache_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_dotenv", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keychain", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keychain-2.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_local_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_secure_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_test", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "frontend_server_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "go_router", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hex", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hex-0.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_multi_server", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "internet_connection_checker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/internet_connection_checker-1.0.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_rpc_2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_rpc_2-3.0.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "json_serializable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "local_auth_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_ios-1.1.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "local_auth_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "local_auth_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "logger", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "lottie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "macros", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "material_design_icons_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "mobile_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-5.2.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "pinenacl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pinenacl-0.6.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointycastle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pool", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "posix", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "protobuf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "qr", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "qr_code_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "qr_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "retrofit", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.5.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "retrofit_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sec-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sentry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sentry_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "share_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "share_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shimmer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "socket_io_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "socket_io_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_helper", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "state_notifier", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timeago", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "timing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "tuple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_graphics_codec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wallet", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/wallet-0.0.13", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web3dart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web3dart-2.7.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "workmanager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "kryptopesa_mobile", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter", "flutterVersion": "3.32.5", "pubCache": "file:///Users/<USER>/.pub-cache"}