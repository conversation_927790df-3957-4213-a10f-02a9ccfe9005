// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFrom<PERSON>(Map<String, dynamic> json) => User(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      profile: UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
      verification: UserVerification.fromJson(
          json['verification'] as Map<String, dynamic>),
      reputation:
          UserReputation.from<PERSON>son(json['reputation'] as Map<String, dynamic>),
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      security: UserSecurity.fromJson(json['security'] as Map<String, dynamic>),
      status: json['status'] as String,
      role: json['role'] as String,
      lastActive: json['lastActive'] == null
          ? null
          : DateTime.parse(json['lastActive'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'phone': instance.phone,
      'profile': instance.profile,
      'verification': instance.verification,
      'reputation': instance.reputation,
      'preferences': instance.preferences,
      'security': instance.security,
      'status': instance.status,
      'role': instance.role,
      'lastActive': instance.lastActive?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      avatar: json['avatar'] as String?,
      bio: json['bio'] as String,
      location: UserLocation.fromJson(json['location'] as Map<String, dynamic>),
      preferredLanguage: json['preferredLanguage'] as String,
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'avatar': instance.avatar,
      'bio': instance.bio,
      'location': instance.location,
      'preferredLanguage': instance.preferredLanguage,
    };

UserLocation _$UserLocationFromJson(Map<String, dynamic> json) => UserLocation(
      country: json['country'] as String,
      city: json['city'] as String,
    );

Map<String, dynamic> _$UserLocationToJson(UserLocation instance) =>
    <String, dynamic>{
      'country': instance.country,
      'city': instance.city,
    };

UserVerification _$UserVerificationFromJson(Map<String, dynamic> json) =>
    UserVerification(
      email: EmailVerification.fromJson(json['email'] as Map<String, dynamic>),
      phone: PhoneVerification.fromJson(json['phone'] as Map<String, dynamic>),
      identity: IdentityVerification.fromJson(
          json['identity'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserVerificationToJson(UserVerification instance) =>
    <String, dynamic>{
      'email': instance.email,
      'phone': instance.phone,
      'identity': instance.identity,
    };

EmailVerification _$EmailVerificationFromJson(Map<String, dynamic> json) =>
    EmailVerification(
      verified: json['verified'] as bool,
      token: json['token'] as String?,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$EmailVerificationToJson(EmailVerification instance) =>
    <String, dynamic>{
      'verified': instance.verified,
      'token': instance.token,
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

PhoneVerification _$PhoneVerificationFromJson(Map<String, dynamic> json) =>
    PhoneVerification(
      verified: json['verified'] as bool,
      code: json['code'] as String?,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$PhoneVerificationToJson(PhoneVerification instance) =>
    <String, dynamic>{
      'verified': instance.verified,
      'code': instance.code,
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

IdentityVerification _$IdentityVerificationFromJson(
        Map<String, dynamic> json) =>
    IdentityVerification(
      verified: json['verified'] as bool,
      documentType: json['documentType'] as String?,
      documentNumber: json['documentNumber'] as String?,
      documentImages: (json['documentImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      verifiedBy: json['verifiedBy'] as String?,
    );

Map<String, dynamic> _$IdentityVerificationToJson(
        IdentityVerification instance) =>
    <String, dynamic>{
      'verified': instance.verified,
      'documentType': instance.documentType,
      'documentNumber': instance.documentNumber,
      'documentImages': instance.documentImages,
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'verifiedBy': instance.verifiedBy,
    };

UserReputation _$UserReputationFromJson(Map<String, dynamic> json) =>
    UserReputation(
      score: (json['score'] as num).toDouble(),
      totalTrades: (json['totalTrades'] as num).toInt(),
      completedTrades: (json['completedTrades'] as num).toInt(),
      cancelledTrades: (json['cancelledTrades'] as num).toInt(),
      disputedTrades: (json['disputedTrades'] as num).toInt(),
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: (json['totalRatings'] as num).toInt(),
    );

Map<String, dynamic> _$UserReputationToJson(UserReputation instance) =>
    <String, dynamic>{
      'score': instance.score,
      'totalTrades': instance.totalTrades,
      'completedTrades': instance.completedTrades,
      'cancelledTrades': instance.cancelledTrades,
      'disputedTrades': instance.disputedTrades,
      'averageRating': instance.averageRating,
      'totalRatings': instance.totalRatings,
    };

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    UserPreferences(
      notifications: NotificationPreferences.fromJson(
          json['notifications'] as Map<String, dynamic>),
      trading:
          TradingPreferences.fromJson(json['trading'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) =>
    <String, dynamic>{
      'notifications': instance.notifications,
      'trading': instance.trading,
    };

NotificationPreferences _$NotificationPreferencesFromJson(
        Map<String, dynamic> json) =>
    NotificationPreferences(
      email: json['email'] as bool,
      push: json['push'] as bool,
      sms: json['sms'] as bool,
    );

Map<String, dynamic> _$NotificationPreferencesToJson(
        NotificationPreferences instance) =>
    <String, dynamic>{
      'email': instance.email,
      'push': instance.push,
      'sms': instance.sms,
    };

TradingPreferences _$TradingPreferencesFromJson(Map<String, dynamic> json) =>
    TradingPreferences(
      autoAcceptOffers: json['autoAcceptOffers'] as bool,
      maxTradeAmount: (json['maxTradeAmount'] as num).toDouble(),
      preferredPaymentMethods:
          (json['preferredPaymentMethods'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
    );

Map<String, dynamic> _$TradingPreferencesToJson(TradingPreferences instance) =>
    <String, dynamic>{
      'autoAcceptOffers': instance.autoAcceptOffers,
      'maxTradeAmount': instance.maxTradeAmount,
      'preferredPaymentMethods': instance.preferredPaymentMethods,
    };

UserSecurity _$UserSecurityFromJson(Map<String, dynamic> json) => UserSecurity(
      twoFactorEnabled: json['twoFactorEnabled'] as bool,
      lastLogin: json['lastLogin'] == null
          ? null
          : DateTime.parse(json['lastLogin'] as String),
      loginAttempts: (json['loginAttempts'] as num).toInt(),
      lockUntil: json['lockUntil'] == null
          ? null
          : DateTime.parse(json['lockUntil'] as String),
    );

Map<String, dynamic> _$UserSecurityToJson(UserSecurity instance) =>
    <String, dynamic>{
      'twoFactorEnabled': instance.twoFactorEnabled,
      'lastLogin': instance.lastLogin?.toIso8601String(),
      'loginAttempts': instance.loginAttempts,
      'lockUntil': instance.lockUntil?.toIso8601String(),
    };
