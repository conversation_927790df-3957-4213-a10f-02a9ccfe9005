// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      identifier: json['identifier'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'password': instance.password,
    };

RegisterRequest _$RegisterRequestFromJson(Map<String, dynamic> json) =>
    RegisterRequest(
      username: json['username'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      password: json['password'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      country: json['country'] as String,
      city: json['city'] as String,
    );

Map<String, dynamic> _$RegisterRequestToJson(RegisterRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'email': instance.email,
      'phone': instance.phone,
      'password': instance.password,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'country': instance.country,
      'city': instance.city,
    };

ResetPasswordRequest _$ResetPasswordRequestFromJson(
        Map<String, dynamic> json) =>
    ResetPasswordRequest(
      token: json['token'] as String,
      newPassword: json['newPassword'] as String,
    );

Map<String, dynamic> _$ResetPasswordRequestToJson(
        ResetPasswordRequest instance) =>
    <String, dynamic>{
      'token': instance.token,
      'newPassword': instance.newPassword,
    };

ChangePasswordRequest _$ChangePasswordRequestFromJson(
        Map<String, dynamic> json) =>
    ChangePasswordRequest(
      currentPassword: json['currentPassword'] as String,
      newPassword: json['newPassword'] as String,
    );

Map<String, dynamic> _$ChangePasswordRequestToJson(
        ChangePasswordRequest instance) =>
    <String, dynamic>{
      'currentPassword': instance.currentPassword,
      'newPassword': instance.newPassword,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      token: json['token'] as String?,
      refreshToken: json['refreshToken'] as String?,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

BiometricLoginRequest _$BiometricLoginRequestFromJson(
        Map<String, dynamic> json) =>
    BiometricLoginRequest(
      userId: json['userId'] as String,
      biometricData: json['biometricData'] as String,
      deviceId: json['deviceId'] as String,
    );

Map<String, dynamic> _$BiometricLoginRequestToJson(
        BiometricLoginRequest instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'biometricData': instance.biometricData,
      'deviceId': instance.deviceId,
    };

EmailVerificationRequest _$EmailVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    EmailVerificationRequest(
      token: json['token'] as String,
    );

Map<String, dynamic> _$EmailVerificationRequestToJson(
        EmailVerificationRequest instance) =>
    <String, dynamic>{
      'token': instance.token,
    };

PhoneVerificationRequest _$PhoneVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    PhoneVerificationRequest(
      code: json['code'] as String,
    );

Map<String, dynamic> _$PhoneVerificationRequestToJson(
        PhoneVerificationRequest instance) =>
    <String, dynamic>{
      'code': instance.code,
    };

UpdateProfileRequest _$UpdateProfileRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateProfileRequest(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      bio: json['bio'] as String?,
      city: json['city'] as String?,
      preferredLanguage: json['preferredLanguage'] as String?,
    );

Map<String, dynamic> _$UpdateProfileRequestToJson(
        UpdateProfileRequest instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'bio': instance.bio,
      'city': instance.city,
      'preferredLanguage': instance.preferredLanguage,
    };

UpdatePreferencesRequest _$UpdatePreferencesRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePreferencesRequest(
      notifications: json['notifications'] == null
          ? null
          : NotificationPreferences.fromJson(
              json['notifications'] as Map<String, dynamic>),
      trading: json['trading'] == null
          ? null
          : TradingPreferences.fromJson(
              json['trading'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UpdatePreferencesRequestToJson(
        UpdatePreferencesRequest instance) =>
    <String, dynamic>{
      'notifications': instance.notifications,
      'trading': instance.trading,
    };

IdentityVerificationRequest _$IdentityVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    IdentityVerificationRequest(
      documentType: json['documentType'] as String,
      documentNumber: json['documentNumber'] as String,
      documentImages: (json['documentImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$IdentityVerificationRequestToJson(
        IdentityVerificationRequest instance) =>
    <String, dynamic>{
      'documentType': instance.documentType,
      'documentNumber': instance.documentNumber,
      'documentImages': instance.documentImages,
    };

TwoFactorSetupRequest _$TwoFactorSetupRequestFromJson(
        Map<String, dynamic> json) =>
    TwoFactorSetupRequest(
      secret: json['secret'] as String,
      code: json['code'] as String,
    );

Map<String, dynamic> _$TwoFactorSetupRequestToJson(
        TwoFactorSetupRequest instance) =>
    <String, dynamic>{
      'secret': instance.secret,
      'code': instance.code,
    };

TwoFactorVerifyRequest _$TwoFactorVerifyRequestFromJson(
        Map<String, dynamic> json) =>
    TwoFactorVerifyRequest(
      code: json['code'] as String,
    );

Map<String, dynamic> _$TwoFactorVerifyRequestToJson(
        TwoFactorVerifyRequest instance) =>
    <String, dynamic>{
      'code': instance.code,
    };

SessionInfo _$SessionInfoFromJson(Map<String, dynamic> json) => SessionInfo(
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      ipAddress: json['ipAddress'] as String,
      userAgent: json['userAgent'] as String,
      lastActive: DateTime.parse(json['lastActive'] as String),
      isCurrent: json['isCurrent'] as bool,
    );

Map<String, dynamic> _$SessionInfoToJson(SessionInfo instance) =>
    <String, dynamic>{
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'lastActive': instance.lastActive.toIso8601String(),
      'isCurrent': instance.isCurrent,
    };

LoginHistory _$LoginHistoryFromJson(Map<String, dynamic> json) => LoginHistory(
      id: json['id'] as String,
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      ipAddress: json['ipAddress'] as String,
      userAgent: json['userAgent'] as String,
      location: json['location'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      successful: json['successful'] as bool,
      failureReason: json['failureReason'] as String?,
    );

Map<String, dynamic> _$LoginHistoryToJson(LoginHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'location': instance.location,
      'timestamp': instance.timestamp.toIso8601String(),
      'successful': instance.successful,
      'failureReason': instance.failureReason,
    };
