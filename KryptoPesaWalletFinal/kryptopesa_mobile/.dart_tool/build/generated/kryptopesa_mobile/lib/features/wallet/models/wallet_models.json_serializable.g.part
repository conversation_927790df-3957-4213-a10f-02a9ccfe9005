// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Wallet _$WalletFromJson(Map<String, dynamic> json) => Wallet(
      id: json['id'] as String,
      userId: json['userId'] as String,
      addresses:
          WalletAddresses.fromJson(json['addresses'] as Map<String, dynamic>),
      balances: (json['balances'] as List<dynamic>)
          .map((e) => CryptoBalance.fromJson(e as Map<String, dynamic>))
          .toList(),
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      security:
          WalletSecurity.fromJson(json['security'] as Map<String, dynamic>),
      preferences: WalletPreferences.fromJson(
          json['preferences'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WalletToJson(Wallet instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'addresses': instance.addresses,
      'balances': instance.balances,
      'transactions': instance.transactions,
      'security': instance.security,
      'preferences': instance.preferences,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

WalletAddresses _$WalletAddressesFromJson(Map<String, dynamic> json) =>
    WalletAddresses(
      ethereum:
          EthereumAddress.fromJson(json['ethereum'] as Map<String, dynamic>),
      bitcoin: json['bitcoin'] == null
          ? null
          : BitcoinAddress.fromJson(json['bitcoin'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WalletAddressesToJson(WalletAddresses instance) =>
    <String, dynamic>{
      'ethereum': instance.ethereum,
      'bitcoin': instance.bitcoin,
    };

EthereumAddress _$EthereumAddressFromJson(Map<String, dynamic> json) =>
    EthereumAddress(
      address: json['address'] as String,
      publicKey: json['publicKey'] as String,
      derivationPath: json['derivationPath'] as String,
    );

Map<String, dynamic> _$EthereumAddressToJson(EthereumAddress instance) =>
    <String, dynamic>{
      'address': instance.address,
      'publicKey': instance.publicKey,
      'derivationPath': instance.derivationPath,
    };

BitcoinAddress _$BitcoinAddressFromJson(Map<String, dynamic> json) =>
    BitcoinAddress(
      address: json['address'] as String,
      publicKey: json['publicKey'] as String,
      derivationPath: json['derivationPath'] as String,
    );

Map<String, dynamic> _$BitcoinAddressToJson(BitcoinAddress instance) =>
    <String, dynamic>{
      'address': instance.address,
      'publicKey': instance.publicKey,
      'derivationPath': instance.derivationPath,
    };

CryptoBalance _$CryptoBalanceFromJson(Map<String, dynamic> json) =>
    CryptoBalance(
      symbol: json['symbol'] as String,
      contractAddress: json['contractAddress'] as String?,
      network: json['network'] as String,
      balance: json['balance'] as String,
      decimals: (json['decimals'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      priceUSD: (json['priceUSD'] as num?)?.toDouble(),
      change24h: (json['change24h'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CryptoBalanceToJson(CryptoBalance instance) =>
    <String, dynamic>{
      'symbol': instance.symbol,
      'contractAddress': instance.contractAddress,
      'network': instance.network,
      'balance': instance.balance,
      'decimals': instance.decimals,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'priceUSD': instance.priceUSD,
      'change24h': instance.change24h,
    };

Transaction _$TransactionFromJson(Map<String, dynamic> json) => Transaction(
      hash: json['hash'] as String,
      type: $enumDecode(_$TransactionTypeEnumMap, json['type']),
      symbol: json['symbol'] as String,
      amount: json['amount'] as String,
      from: json['from'] as String,
      to: json['to'] as String,
      network: json['network'] as String,
      gasUsed: (json['gasUsed'] as num).toInt(),
      gasPrice: json['gasPrice'] as String,
      fee: json['fee'] as String,
      status: $enumDecode(_$TransactionStatusEnumMap, json['status']),
      confirmations: (json['confirmations'] as num).toInt(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      memo: json['memo'] as String?,
      relatedTradeId: json['relatedTradeId'] as String?,
    );

Map<String, dynamic> _$TransactionToJson(Transaction instance) =>
    <String, dynamic>{
      'hash': instance.hash,
      'type': _$TransactionTypeEnumMap[instance.type]!,
      'symbol': instance.symbol,
      'amount': instance.amount,
      'from': instance.from,
      'to': instance.to,
      'network': instance.network,
      'gasUsed': instance.gasUsed,
      'gasPrice': instance.gasPrice,
      'fee': instance.fee,
      'status': _$TransactionStatusEnumMap[instance.status]!,
      'confirmations': instance.confirmations,
      'timestamp': instance.timestamp.toIso8601String(),
      'memo': instance.memo,
      'relatedTradeId': instance.relatedTradeId,
    };

const _$TransactionTypeEnumMap = {
  TransactionType.send: 'send',
  TransactionType.receive: 'receive',
  TransactionType.escrowFund: 'escrow_fund',
  TransactionType.escrowRelease: 'escrow_release',
};

const _$TransactionStatusEnumMap = {
  TransactionStatus.pending: 'pending',
  TransactionStatus.confirmed: 'confirmed',
  TransactionStatus.failed: 'failed',
};

WalletSecurity _$WalletSecurityFromJson(Map<String, dynamic> json) =>
    WalletSecurity(
      backupCompleted: json['backupCompleted'] as bool,
      backupDate: json['backupDate'] == null
          ? null
          : DateTime.parse(json['backupDate'] as String),
      lastAccessDate: DateTime.parse(json['lastAccessDate'] as String),
    );

Map<String, dynamic> _$WalletSecurityToJson(WalletSecurity instance) =>
    <String, dynamic>{
      'backupCompleted': instance.backupCompleted,
      'backupDate': instance.backupDate?.toIso8601String(),
      'lastAccessDate': instance.lastAccessDate.toIso8601String(),
    };

WalletPreferences _$WalletPreferencesFromJson(Map<String, dynamic> json) =>
    WalletPreferences(
      defaultNetwork: json['defaultNetwork'] as String,
      autoRefreshBalances: json['autoRefreshBalances'] as bool,
      transactionNotifications: json['transactionNotifications'] as bool,
    );

Map<String, dynamic> _$WalletPreferencesToJson(WalletPreferences instance) =>
    <String, dynamic>{
      'defaultNetwork': instance.defaultNetwork,
      'autoRefreshBalances': instance.autoRefreshBalances,
      'transactionNotifications': instance.transactionNotifications,
    };

SendTransactionRequest _$SendTransactionRequestFromJson(
        Map<String, dynamic> json) =>
    SendTransactionRequest(
      toAddress: json['toAddress'] as String,
      amount: json['amount'] as String,
      symbol: json['symbol'] as String,
      network: json['network'] as String,
      gasPrice: json['gasPrice'] as String?,
      gasLimit: (json['gasLimit'] as num?)?.toInt(),
      memo: json['memo'] as String?,
    );

Map<String, dynamic> _$SendTransactionRequestToJson(
        SendTransactionRequest instance) =>
    <String, dynamic>{
      'toAddress': instance.toAddress,
      'amount': instance.amount,
      'symbol': instance.symbol,
      'network': instance.network,
      'gasPrice': instance.gasPrice,
      'gasLimit': instance.gasLimit,
      'memo': instance.memo,
    };

TransactionFeeEstimate _$TransactionFeeEstimateFromJson(
        Map<String, dynamic> json) =>
    TransactionFeeEstimate(
      network: json['network'] as String,
      symbol: json['symbol'] as String,
      gasPrice: json['gasPrice'] as String?,
      gasLimit: (json['gasLimit'] as num?)?.toInt(),
      estimatedFee: json['estimatedFee'] as String,
      estimatedFeeUSD: json['estimatedFeeUSD'] as String?,
      estimatedMinutes: (json['estimatedMinutes'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TransactionFeeEstimateToJson(
        TransactionFeeEstimate instance) =>
    <String, dynamic>{
      'network': instance.network,
      'symbol': instance.symbol,
      'gasPrice': instance.gasPrice,
      'gasLimit': instance.gasLimit,
      'estimatedFee': instance.estimatedFee,
      'estimatedFeeUSD': instance.estimatedFeeUSD,
      'estimatedMinutes': instance.estimatedMinutes,
    };

PortfolioValue _$PortfolioValueFromJson(Map<String, dynamic> json) =>
    PortfolioValue(
      totalValue: (json['totalValue'] as num).toDouble(),
      currency: json['currency'] as String,
      breakdown: (json['breakdown'] as List<dynamic>)
          .map((e) => PortfolioBreakdown.fromJson(e as Map<String, dynamic>))
          .toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PortfolioValueToJson(PortfolioValue instance) =>
    <String, dynamic>{
      'totalValue': instance.totalValue,
      'currency': instance.currency,
      'breakdown': instance.breakdown,
      'timestamp': instance.timestamp.toIso8601String(),
    };

PortfolioBreakdown _$PortfolioBreakdownFromJson(Map<String, dynamic> json) =>
    PortfolioBreakdown(
      symbol: json['symbol'] as String,
      balance: (json['balance'] as num).toDouble(),
      price: (json['price'] as num).toDouble(),
      value: (json['value'] as num).toDouble(),
      percentage: (json['percentage'] as num).toDouble(),
    );

Map<String, dynamic> _$PortfolioBreakdownToJson(PortfolioBreakdown instance) =>
    <String, dynamic>{
      'symbol': instance.symbol,
      'balance': instance.balance,
      'price': instance.price,
      'value': instance.value,
      'percentage': instance.percentage,
    };

CryptoPrices _$CryptoPricesFromJson(Map<String, dynamic> json) => CryptoPrices(
      prices: (json['prices'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, e as Map<String, dynamic>),
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$CryptoPricesToJson(CryptoPrices instance) =>
    <String, dynamic>{
      'prices': instance.prices,
      'timestamp': instance.timestamp.toIso8601String(),
    };
