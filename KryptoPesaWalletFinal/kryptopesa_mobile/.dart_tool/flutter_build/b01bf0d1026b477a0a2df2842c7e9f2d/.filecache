{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/engine/parser/parser.dart", "hash": "ec31b6e3a15e29f639a8042ba10f9c0a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_properties.dart", "hash": "953396d57b69e0e889d9dfcc4f7fdabe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/crypt32.g.dart", "hash": "8898ba9f5064edff3e9fbc9889ba9dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationandcondition.dart", "hash": "c3b42ddc5c69d20f4bbfb3ccb3f30ffc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/replay_recorder.dart", "hash": "958bda09c5933232036e590f08665b17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "hash": "f381ed91de52f40a7dff4d2f0f3f6d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/flutter_sentry_attachment.dart", "hash": "b3ed9b3feee936f65f958bd74604d226"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/person.dart", "hash": "a0f12d72bbc64d6edba6d1174d5603e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_null.dart", "hash": "44b633ff9265431f3dbf4aa9d0b5009b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_enumerated.dart", "hash": "8ad5dccfca7e8306466bc4d4edf46a1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/variant.dart", "hash": "0564ee9e759fe52b58de8af3d5d0f9b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/reflection/iterable.dart", "hash": "763f95cfee7dc8f35f6557eab7e94312"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/lib/src/windows_version_helper.dart", "hash": "1f4f05a739274cdeb88c110bc6561ae8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement7.dart", "hash": "f05adccad12249a4f175efc9b8abfb37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ccm.dart", "hash": "41f36ff57afc9c456e29620d8e0d8efc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "hash": "ce30848ef1f94b243d6094ee0d740597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/method_channel/method_channel_firebase.dart", "hash": "a3239e1caa780c64918575ebdd5dd4cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/misc/extensions.dart", "hash": "033cc457821088f152cc31f4439f9f0d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_metadata.dart", "hash": "201005c585ce255343e625b1a5e49601"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/tracing_client.dart", "hash": "9c3daaa3377eb15f29247624afa63ac5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp160r1.dart", "hash": "18f36ed422693224b3733071e73c25e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/platform_checker.dart", "hash": "e1ed0970ec9b3f01c7b5b9b469b772e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdeviceenumerator.dart", "hash": "8a2e692d7adcf4c9e0bd0f85979ab7c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "34336c7c021e6749ef0bd6a11e48f887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_item_type.dart", "hash": "e94fd0b15ea326daa115fdaab61d3f33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_authenticated_safe.dart", "hash": "7dea8c8f45a4d1fd5f2d9113ce7228c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/route.dart", "hash": "da1662b9c25c25360cd487c1b4ade3ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/factory.dart", "hash": "ac6f643506403263b5514a54d09baa11"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1set.dart", "hash": "27a0bdbf6e90ba19c5f6c80fe677387e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/block_ctr_random.dart", "hash": "4b0e65a90e7a1fa31571f3eb07023516"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/features/auth/providers/auth_provider.dart", "hash": "a75372bdea0a9aa9a61ac1848df03bdc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/iphlpapi.g.dart", "hash": "90687597d058691ddabaa9819ebe2441"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha256.dart", "hash": "1ab8b7099e1db649e9b7337d005dc3fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file/io_multipart_file.dart", "hash": "8094c68b4a15e6f73e09501aa6ff4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/gdi32.g.dart", "hash": "3235bc280cf19dc53be8f10c56d89beb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/utils.dart", "hash": "2ca48ade734e30ac0aa95393df8e12d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "hash": "25c44b3908d2602e0df540ca5b17da27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/exception.dart", "hash": "e625c15c91736f7c029d22eaf13a9599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "hash": "0f0fc7bc5c7c2c1581ed2ed245baf136"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/shake.dart", "hash": "8b81bdca02ee9cea674d06d456940b5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange.dart", "hash": "46d014f5f4ff404b81098da9b003b770"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/utils.dart", "hash": "42550f7112e7e8c786999aab0e356482"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/read_write_sync.dart", "hash": "e3f76b424ad53ae6f603bf9a0662e148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/itypeinfo.dart", "hash": "d1242664c894dd9825221a49693814f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "hash": "75290287531fff47b4eab2f25f050d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_storage_backend_preference.dart", "hash": "bd95228b199ffc9f775bb4e037a461ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_response.dart", "hash": "f29d1458f73f015dabefc27f98181f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/lib/src/share_plus_windows.dart", "hash": "21cf4194f037cc25c586d1c929623956"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/widgets_binding_integration.dart", "hash": "9423771f9ffb043cc46df8d275f23339"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechbasestream.dart", "hash": "1632b8b538a5115973c424adb5380d7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_baggage.dart", "hash": "bc6fdd45b50ca2873ce283ade0aafb8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_match_rule.dart", "hash": "0298dac3221d4c6752b6207594e4f470"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/sic.dart", "hash": "5ce7d9fedfa2c40efbf00c41224759dd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/user32.g.dart", "hash": "f7d24a92e50e72cd80aab0301eef14ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "51d92d191bdfceacf4cc7381782d4e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160r2.dart", "hash": "4199638bf85e837947f14a023bba818d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/platform_interface/platform_interface_firebase_app.dart", "hash": "209399f0e6f16675c3f087b8eb17087b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160k1.dart", "hash": "9c471f292c3ffd4fd6519b21ccc9ff07"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/io_client.dart", "hash": "278e31e5169963c0c3ea3ee9b4bb4469"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iunknown.dart", "hash": "314ca45445509ac0635a48d2dacca294"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "ffd004f95154cc4fe026271fb8aed8cb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry.dart", "hash": "56f8eb85318d98fe58688289b90fdc36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoicestatus.dart", "hash": "5164e5af0ccfe7dbe777bb588e91c937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wtsapi32.g.dart", "hash": "da654b6ae25dd581a1b5f1084d769c91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/stacktrace_utils.dart", "hash": "6f0d07b7dfd8c0a548ec75a034cd52aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecc_base.dart", "hash": "468fe17ab42c185be969ed1eeab7f8db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/LICENSE", "hash": "6eb17212266d6f143295fbec385617aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "3b32647556f88ddd6d625ddc58c7691e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/lib/firebase_messaging.dart", "hash": "3110d5ac01cbcb85c2a50744567907f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/java/android_replay_recorder.dart", "hash": "aeb9ec762f11029e394a2ac9bfc79f29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1printablestring.dart", "hash": "a083d071461a2f6eb2effab7f2c1c2d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/linux/file_picker_linux.dart", "hash": "fdd54d489ffa1481f24f03670c648423"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/lib/src/share_plus_linux.dart", "hash": "37f5d64c830cc88f99153eef75047beb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/secure_random_base.dart", "hash": "a5293efb82d89d837837df349d03ff7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/annotations/hive_field.dart", "hash": "c01f3dc3ecfb5ddf08d6b002c90aabfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ige.dart", "hash": "770bfccaae45c6029de0af97430cc4a5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/encode.dart", "hash": "4dfc59ebfe657a9fe7ac04ac5b9f70d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/headers.dart", "hash": "12ada90523ca5fc00e317c0a59889a1c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha384.dart", "hash": "9b963ea0af13edbb8f022ff4b9f58c55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/binding_wrapper.dart", "hash": "286137ae9ff123221d3ce79b4f8b7508"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader7.dart", "hash": "a60dd773b7d69b347521fb64257f9397"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_certification_request_info.dart", "hash": "a4aab76e29682587c7cb505b1b1c9fc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclockadjustment.dart", "hash": "dde1235f5cf091fe6d4a2938399afb4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/ecdsa_signer.dart", "hash": "04df49115f42e6aba6c7182784ef0247"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/platform_info.dart", "hash": "81e7d988ce6f8a20230e61cdac83f21f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement8.dart", "hash": "befbfd864024e35cb6b7752f9b4ac4d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader4.dart", "hash": "5a65f8839771af0fad5b2cf647703264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/hive_flutter.dart", "hash": "ed6800e3fdfd2d724c29415c77a47dc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/util/event_emitter.dart", "hash": "61212c00714faaecacdea8651e2001c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/keccak_engine.dart", "hash": "4b47b39739cb8185654484fa9a440d41"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/utils/logger.dart", "hash": "083654d0c4f4718310f51293f14339c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/utils.dart", "hash": "4904f65d68a0e23943366dd258084968"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/services/navigation_service.dart", "hash": "687db821a3f5316e22ac6840c35e77c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "0b1037c34b64b5d7d84c6e578ab59974"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/fortuna_random.dart", "hash": "db1dccf6cafeea98e1986f83b2978be7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/src/exception.dart", "hash": "9a74595c2e95795b6c96d74f2b6bcca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/LICENSE", "hash": "b93fe5bcbea09a4ba86ec85c6bb8baaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ct.dart", "hash": "dfe75b0d095b9d0520e61e45b3a19be1"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/services/notification_service.dart", "hash": "520b0436a5cba28875e379f4ba13c0e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/utils.dart", "hash": "e8c1fb168963c9e062a369d72d2dad6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/notification_details.dart", "hash": "7c5b14805d686129e90293ef086d4d68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/intl.dart", "hash": "f0dd0e0193ab6bc6a1dc2a6cf6e1cd6b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/platform_file.dart", "hash": "4f4268ad277e47f47cddd0df860dcf1a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1objectidentifier.dart", "hash": "5506829f2564c63690458f8d031b85b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_builder.dart", "hash": "bc1f35bad7b3fd785bd8734292b27ff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "7eae5454728dc152e90d36cc6b715544"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_rdn.dart", "hash": "4c085993923bdb9c4040d1ae74c6a280"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/api.dart", "hash": "35da1e0ac4001efabe27f201740996b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/cancel_token.dart", "hash": "c9f037c19c2e4adfe331d9e56f2e72c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/timeout.dart", "hash": "6665bae4ddca65609834735a7f24c95f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/LICENSE", "hash": "e6465045b7ce261fdfe7f17c9bff6249"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "f42009fc52ad811f1d34405961c63183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/form_data.dart", "hash": "bfd57391197129cbe3c47c75b2c21672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechwaveformatex.dart", "hash": "8d9c84de01d7084606586631ac759a34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifilesavedialog.dart", "hash": "de786aad9aba3c37b121a1f0238119a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/access_rights.dart", "hash": "571943578e78006ea11a53ba5a541224"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_platform_interface.dart", "hash": "022ddffcb01934fc1b0912fcb38de832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfilesenumerator.dart", "hash": "c72923f8ad46feb8bcf25ecbd0379294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v2.dart", "hash": "7aeb1366a4c73e08ca0012106f87cb8b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager.dart", "hash": "53ef1e482a9021fe353d68c9f8a1affc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_reader_impl.dart", "hash": "7a1a5e4d4978935357c5815297b253f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement5.dart", "hash": "7787380533fd85067e9c4303a9564dbb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemobjectaccess.dart", "hash": "3ce0f30d7026f6462449617764734437"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/rc2_engine.dart", "hash": "25f9d9a63608d55e043f713f8befe7f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "a4578c9e939f9a7aec6e8897e055b6ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/padded_block_cipher/padded_block_cipher_impl.dart", "hash": "36341e026a32e13852f8dc3f7ba49f5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/hint.dart", "hash": "7c4de09d17b9b8dc423919b57828d190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/sentry_native_channel.dart", "hash": "fb20b19ebf2275d62beddc7e06162166"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/keystore.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspectable.dart", "hash": "a8d03ee07caa5c7bca8609694786bbf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "0cd72a3b3ab10728d2b3234014f43d83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_trace_context.dart", "hash": "8ae3fe134236bc358f7a38bc3851d3d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/signer.dart", "hash": "83b3dc31517e191dc194852894ba25bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/win32.dart", "hash": "018e93669d12c52b66204d139de58ef8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_transaction_name_source.dart", "hash": "ff1bdfbdeb2980ca578db680bf476356"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/tiger.dart", "hash": "2a2fe8fec0bba8749eb1a1a4a8f16e7c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/bitmap.dart", "hash": "30207bb624460e743b557f58e7b39479"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "f5defa76a8d0e0a0a5d1670fbc270cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_mac_data.dart", "hash": "01c56cbc4a5e25f4c5b3353c402f4304"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.g.dart", "hash": "ebee8885b5afd397cfa8920eeccf88e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "88956349d04ce0c5fc6ae1e89fd65b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp224k1.dart", "hash": "a6159ee3c90b2196e3fab3dc5b1b51f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "dd15fe8e4901c3c57a40bed6b0079e80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/helpers.dart", "hash": "20e259f655329b9bc2ecb98ae2975e72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/environment/_io_environment_variables.dart", "hash": "a1d8d3ff80739dcd4a36f964a934b9a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/replay_event_processor.dart", "hash": "df82da310cd1a8ba7611492c8f386cdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md5.dart", "hash": "5828730d8f759fcbfa757299a6b2c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/LICENSE", "hash": "906742df8afd59744edfde69b6b6f7e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/rc2_parameters.dart", "hash": "30cd153e6972aee5f5f3b04cac7383d3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor.dart", "hash": "44f7c82974eac515139b98056bea67ad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_key_info.dart", "hash": "01167f5726fe27d877af4652c1e404a8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irunningobjecttable.dart", "hash": "dfa3a8605c6665c94b8ca2bd43827718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem2.dart", "hash": "908da18a3fee181ac432b85d7097e5f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha1.dart", "hash": "39d5994f96295109bb5aabf8b30e7d37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/secure_random.dart", "hash": "22aeb6b715e318e5a73e1137d665d01c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/not.dart", "hash": "be4332e6d8c10f4a290e2a412399e1cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/subscription_stream.dart", "hash": "45f0e675fa74d765bee71cf2c553bd58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/builder.dart", "hash": "d2b684e31e63b6c876b2c0266705447a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/parser.dart", "hash": "a54725bc16ee2ca993762c441542c1cc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/native_sdk_integration.dart", "hash": "b0bb36d8ec1c9c0ea42c85db1a1525f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "hash": "43465f3d93317f24a42a4f1dd5dc012e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_random.dart", "hash": "fff3d52d873f92db7ebe6915f5a933c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_span.dart", "hash": "4428b87cbdb619dc1c2edbae9184efad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/messaging_style_information.dart", "hash": "017129b89f3045aa21d9a8032f5dfec0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/flatten.dart", "hash": "b9f39f1eac6d7a0e9964cb4c7b2cd04a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenser.dart", "hash": "3fc24d3b43ff4a6b63811978cfb697e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp112r2.dart", "hash": "ae864bde681355d21277a204953c8060"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/lazy_box_impl.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/max_body_size.dart", "hash": "585de35d3094c200ced642c0952ea1d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_rpc_2-3.0.3/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/native_app_start.dart", "hash": "d02e2b810c8360a8b255fbc1aeb08114"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_member_name.dart", "hash": "2ef397117616f6ff779ed0ab2dd0d61d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/location.dart", "hash": "6ed688f382406e9c782f92df9e965fac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/type_check_hint.dart", "hash": "544a32573af76328ea61cb97bc81fae3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_address.dart", "hash": "4ecc0e7678d4ed3bf62a04b3e383e424"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/block_cipher.dart", "hash": "ece6cc53a178d8bb370eeb451754f366"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1integer.dart", "hash": "b4268a914c3911e85b21da6ed28e8b0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_private_key_info.dart", "hash": "7a8930e336956f0f5da4125808add64c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/spotlight_http_transport.dart", "hash": "80db134a5a382a032f818be9f5eafd57"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "1b1e7812d9eb9f666db8444d7dde1b20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/exceptions.dart", "hash": "ad84ac2c0607f2ca46d74eb0facbca3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistmemory.dart", "hash": "cdc3ed60fc9f8d6e2fd72afef2012bda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/jvm/jvm_frame.dart", "hash": "9efc18aed2c8a8348ccf1d407eb68252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_operating_system.dart", "hash": "391d1bd963ef385b249c0689a3fef05a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1util.dart", "hash": "6163f4485dae643d418359623a0a8e7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_pkcs12_attribute.dart", "hash": "3801b5a1c67d9ee7d645423ea830bb0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/file_system_transport.dart", "hash": "041949493211aadcdda67c5e1d92f0f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_run_zoned_guarded.dart", "hash": "c5203196532d2fb38254d5345f108d3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_options.dart", "hash": "859fb3f0e6ed9879424415ff38176c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/callbacks.dart", "hash": "b020749262d0d602700cd21e6f41acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/dotenv.dart", "hash": "fff38fbee67e2f21ac8b871431f3f5aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key.dart", "hash": "e866e738f5ca40c36ac1afe2196066fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/sic.dart", "hash": "c03d43a0d1a5a8207fe25c92c362a7b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_xchb.dart", "hash": "4e85d0625d482a63b08cca9927502aa6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/private_key_parameter.dart", "hash": "132622d54cd53aec2b21591e6b5a90b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/utils/debouncer.dart", "hash": "0df729d571e4e98cf640248d90b4a24d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/combase.dart", "hash": "90ed8a12c97e362a162da690203df055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_options.dart", "hash": "a8153d07f9e065e3e1066c4c616e1ee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/cbc_block_cipher_mac.dart", "hash": "4818af41263ddf07c4f6462457ae28d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md4.dart", "hash": "3c45979b68b6aa985418f9a03a5b79ad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/security/biometric_service.dart", "hash": "55d2640ba45c93062bde1f536734f621"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/istream.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_envelope_item.dart", "hash": "fcea8da1259945811efa3656ac557761"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuri.dart", "hash": "ed8502a630b1e3004b3e0469816899d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/src/firebase_app.dart", "hash": "92822ea2edbf875f4f02ee0df917a511"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/private_key.dart", "hash": "043e8bef19eb143ecd9d3db40a080b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_user.dart", "hash": "347ee469519077f271b2125e85df9b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/pubspec.yaml", "hash": "cb5ce6219d477ee6ac33b54e4562b3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_a.dart", "hash": "24b4ee2e73dff7663abc3e9e83528625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/default_method_channel_platform.dart", "hash": "c7c117f8683bd3335fc56fb4e706d271"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_asset_bundle.dart", "hash": "22cb66140bab843366d740f30bb8954b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/encrypter.dart", "hash": "94375912ec651e0d394d4991f6c61d5f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/src/interop_shimmer.dart", "hash": "1a833c7ff6536db146718044059a5b3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/client_reports/client_report_recorder.dart", "hash": "1039b6fc4459366a776c37fa7a2a0870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfoldermanager.dart", "hash": "49703a6e2b7dff13d801b6eb6e02062c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/linux/dialog_handler.dart", "hash": "5c44e33ae64d25992b7fbf2390070377"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/android_device_info.dart", "hash": "9bf109ecb182b597064ca9ae6f68b801"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ctr.dart", "hash": "154d573def33d0ada1c6dfc13b6a8feb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpoint.dart", "hash": "96c9d801d1879091246f0b107ee4147e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_stack_frame.dart", "hash": "3231afa7842e4ce0394a40cc21f2f679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1teletextstring.dart", "hash": "57a7e11994ebd3d945aef5481188bea1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "c96999a0782dffe9bf8eeaf394caf3bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/ecdh_kdf.dart", "hash": "d23f9307b748f3d399fa8578e9fdbd66"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement3.dart", "hash": "ee2f81dc37bb6d1adb9570b7634eed77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isupporterrorinfo.dart", "hash": "0fe168f7fefcc6e38cea5a1daaa08fe7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "ef5d77a8181065ceb0e93986c1a6f6ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_generator.dart", "hash": "644bc34867e359c425f1a2d8fd16348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imoniker.dart", "hash": "59c4492b4ff3d2e5424c1903bcb8a271"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_derivator.dart", "hash": "23349969cad5e562b8bc82bb068b8b9c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/windows_device_info.dart", "hash": "93faa63229d86e5b66c6fa4fd0554f1f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellingerror.dart", "hash": "c8ff0e27e7c87256a90d8a3ef24be6ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemrefresher.dart", "hash": "5026f3bc8f63a10ffb208a35e304f40c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/view_hierarchy/view_hierarchy_integration.dart", "hash": "55d77687f0106015861768f823b55e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iprovideclassinfo.dart", "hash": "74801cb491652ec4ce96fe1f4646836a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/recorder.dart", "hash": "7ae70f2cfdd5737fddea2cee29446b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_subject_public_key_info.dart", "hash": "cd3bfd17cbd8f8ff1cb9b96ab863d2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/linux/kdialog_handler.dart", "hash": "070342f2996d99a4df51146a70e2e53f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/concat_kdf.dart", "hash": "2c9cf8b4e1efe153fd93f70e73ed3e9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/greedy.dart", "hash": "c138ee7ea69a6621403b3ba6973d4d7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_baggage_header.dart", "hash": "59960e9fc54d14fe88b0854281bef986"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/remote_notification.dart", "hash": "b652111e5bc8c0e52096430d481ff3dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/sentry_http_client.dart", "hash": "71070a162c924d076faf052c7bc4c73c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/frame.dart", "hash": "e28d4397780eecba27eaced013118898"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/notification_info.dart", "hash": "5f02ac3087f8d081f489730eecb97f70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_browser.dart", "hash": "226dcee375419d2972b34ae662b42cbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/bound_multipart_stream.dart", "hash": "8e2dfba570ecd1895c50258939b609a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winrt_helpers.dart", "hash": "8a032ca2b66b8be21ce8368f80406db7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sdk_info.dart", "hash": "8eccbcd692f9b17caa55cd5ccb83a443"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ntdll.g.dart", "hash": "72e3f09580a88c2aa3ce838611e0a25d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/sentry_screenshot_quality.dart", "hash": "f77dd4e0558386fe5aeecd18c4e46ddd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithm.dart", "hash": "c9387406fe26aecc079e1836e5be70f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/LICENSE", "hash": "fcc4d991b068e4103c4ef152baf65fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/method_channel/method_channel_device_info.dart", "hash": "47692aa3b3506b6ac3f2639c4c78cb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/initialization_settings.dart", "hash": "289e35ca06df4ab99b530bc3e9dd4766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winscard.g.dart", "hash": "f0ffece0b01158318dbca4d043da78b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwinhttprequest.dart", "hash": "e9c0088ee89cdab9346358a1ab7d4f18"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/hub_adapter.dart", "hash": "a6403c647919abe8396404883e480639"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_impl.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp192k1.dart", "hash": "********************************"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/storage/secure_storage.dart", "hash": "27be964a874b433fecbb7265d5756609"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_collection_mixin.dart", "hash": "3acf14588aeccbac8c5d9e50e5db9edb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/typedefs.dart", "hash": "4c00fd95f493a02179f1013a29629e43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/native_app_start_integration.dart", "hash": "e3f0c14949454d8d8cd24d2b9f13c31e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_buffer.dart", "hash": "99760254cc7c1941d4d7d7bb0fad045d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/connectivity/noop_connectivity_provider.dart", "hash": "38028420918cc3e578d37141e6e46eca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_flutter.dart", "hash": "6d6cc28002eb0f6e5098697ad4958ebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs7/asn1_content_info.dart", "hash": "145a8a4316985af89a3dc6736ed3178f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/sentry_flutter.dart", "hash": "7a97bb736d29d7b879b015e0051a13ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "1325fce32c39a3792e3eeab612f942f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_value_type.dart", "hash": "d2abc4bf73a6fd6720bf0bc3b1a461f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_macos.dart", "hash": "03646ad7ecc4178fcf9509961aa7e424"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive_impl.dart", "hash": "17d6409e5c71813bb1715f370eca420a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "45a4d78a037bdf56e5eb45d75298ff84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/on.dart", "hash": "72e3dab849e56dd4bf487065a0d92ef5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_event.dart", "hash": "95c1151586a0a2a83f5ba58eed69b877"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevicecollection.dart", "hash": "a45b41e12ba5853543f707ce7dbab9d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_safe_contents.dart", "hash": "cd768a314319e35ac7f3d960700c7aba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/desede_parameters.dart", "hash": "29fcb9fa6ab76c0fff43a12a248c46ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box_collection/box_collection_stub.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/exception_type_identifier.dart", "hash": "c2200418539ee949e6e4a0ae1ed3ae80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/flutter_local_notifications_platform_interface.dart", "hash": "64aba6b07ccfb43755f1c29955134b53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/native_memory.dart", "hash": "ba7d341ca61d143b077fc18f8656d1a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/network/api_client.dart", "hash": "eddc8e093b5a3304c32b41c867a26e50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/src/local_auth.dart", "hash": "1feadb70762e4b4efb3c01f222821fe0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/tracing_utils.dart", "hash": "aece59b204b9416834b070a7f91e35f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/lib/platform_interface/share_plus_platform.dart", "hash": "42219310e1446f03960d306ae39b93e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemlocator.dart", "hash": "84516bb884e27c54321d286d9ae9e9d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "f4b8510296da48652b91a91857d7c71b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/sentry_native_binding.dart", "hash": "783ee8e68facbc5770c0fe1b2fad8e7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/native_app_start_handler.dart", "hash": "6bf997d2e7bb8f810bb07bd7aef0e23e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_details.dart", "hash": "f61c50d40c00ac96c595ea0b2682937c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/buffered_file_writer.dart", "hash": "83ad6899b262c42a494ebce50a8974a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/connectivity_plus.dart", "hash": "9b43d6f9384a837bbd0d8474e2365c7a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/rc4_engine.dart", "hash": "bb2f5c45f97f6569c5ff88f1c16eed7b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/LICENSE", "hash": "3d853fa9263f8487dd82234ed6b56931"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/noop_client.dart", "hash": "7fa2c1940b25ca41833aa0ca7a7e8f1d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensormanager.dart", "hash": "af29a3ea1a69b956f7915a4cc29d4b89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient.dart", "hash": "983f9738507c43e2eee65120e25d0785"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winspool.g.dart", "hash": "18e255eb54fef40d17b6f6abac4455aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "034536c8c0bdfd72d8f8060ea1f36f3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/hive_cipher.dart", "hash": "a2716332bd9726a3ab118d6fd896ac17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "34ac34257c6ee30da8c0b6de4d0a5444"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienummoniker.dart", "hash": "3e2ba5ba60ae123aa45ccc5f07eb3ae8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "hash": "75ba7e8a7322214ca6e449d0be23e2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/logger/sqflite_logger.dart", "hash": "ab42e582c15854ab187bc7a07fb8baa5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/builder.dart", "hash": "6722e17c98e37b4ef1b96bf4b17d9008"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_collection.dart", "hash": "f083ee7c0f8875e81b5fd6e33fde3ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/keccak.dart", "hash": "364420045b806c9db17c9d10e91aed8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wevtapi.g.dart", "hash": "4fd8d39dff594e013e042c2896cb0bf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/http_transport_request_handler.dart", "hash": "b5303c019f80af4aa5a510930a931810"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/functions.dart", "hash": "e999eca1c1c76717a74f50814d129d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_client.dart", "hash": "6b3c8cd4c0677edeb4fb8c22d923657c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "hash": "1936d57a483f9894c5b5c3087b446809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/extensions.dart", "hash": "a9e0df3a9079b0f6b5041cf4d901f932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/signer.dart", "hash": "5936497da2572436ae1351e736720360"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestproperties.dart", "hash": "25ff828118233f5852e97c3e15c2a5da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_signal.dart", "hash": "8596b58c127792783625b4b22a4d023c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/LICENSE", "hash": "7b7fcd3f415f29a260e0d5f15c7d9565"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/storage/local_storage.dart", "hash": "d4174ee53b8f84fcefc1fcc9c90deb76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants.dart", "hash": "808711eba7e3374bd5161036905b982d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement.dart", "hash": "e00e5a90ff913bc9c53a6572e53ec576"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/local_auth_darwin.dart", "hash": "2d0b26be5c4a3422283198cf7c138dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dbghelp.g.dart", "hash": "ec2c8a676c3ca12891e9a65ea03458e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idesktopwallpaper.dart", "hash": "28a96a9cad386cca4604fe9b6b0ac250"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "634d273f14a099a4f0bd577979779ee1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/dialogs.dart", "hash": "31ff0d4d17e824e16798aed227f48e88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_isolate_extension.dart", "hash": "e0363ecfdc3c15b735ceb9337151d871"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_hd_key-2.3.0/LICENSE", "hash": "970e8ab5b8a89a57cbd64c9a1f9ddb8b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/load_image_list_integration.dart", "hash": "6cdecaa6c667ea447200d724d731f44e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_measurement.dart", "hash": "168f304e3c0705849cebee06da0c2770"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/pointer_data.dart", "hash": "67f58e8d946fa7138e52e482d7c8f8d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_integer.dart", "hash": "3d8afe52b6baa8d2f4a8209746f41ba3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "868fd1ae52dcd191d04c90dc4a26dfac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "32621d3d5949612fe2c614d37bfaf7e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/enricher/io_platform_memory.dart", "hash": "9b04ff5e9590f5a9949294f2746d0257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1numericstring.dart", "hash": "7f5d429cb9fc83b66071ad938b7e9c19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sec-1.1.0/LICENSE", "hash": "2ce1e5aae6ae0cf3dd243c80b45dc5bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/shared/providers/app_providers.dart", "hash": "24f0989de16ee760aeb837bba2acff19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_sampling_context.dart", "hash": "a28288711a7d76a82acbcc0b26272b73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_client.dart", "hash": "985baf9d2ded07a72062e7c8c7db41d6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport.dart", "hash": "754560d00f3c24825e656e9d7e03fd6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/flutter_local_notifications_platform_linux.dart", "hash": "145a18283aef042bba506a2190347763"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/sentry_safe_method_channel.dart", "hash": "959344c88b2394c28129a9e22201153b"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/services/app_initializer.dart", "hash": "26cf35973280138370466a4150708fcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_utf8_string.dart", "hash": "83bed422d5a5a22b1b980ba4470bbdf7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/b01bf0d1026b477a0a2df2842c7e9f2d/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "hash": "4c5a88901110f96f096d0a05cc607301"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/darty.dart", "hash": "facddfc67b3a866f3b6577f19a2b6c83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/biometric_type.dart", "hash": "5c67019c52b8cc1c9e1d211aaca0f2a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "hash": "95bd0247422d589a2b39cd985a000749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/paddings/iso7816d4.dart", "hash": "913f9b2e892f99726d2ab6210f2f6819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_shared.dart", "hash": "c2f30f0829e63ccf0449de5982e324b4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/sentry_unmask_widget.dart", "hash": "82e509b2ba3631b5e3f465d8565e915d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_traces_sampler.dart", "hash": "202cb2edef6fa75298ed5c30c791a83d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/debug_image.dart", "hash": "6140fbe8ec185ac31ad89e35bc59caa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_salt.dart", "hash": "8477e1afe76cb5334fd6cc42a04d0ff8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/firebase_core.dart", "hash": "c83f257da86decec9584473cf41f0ee0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eip55-1.0.2/LICENSE", "hash": "6381be17811e174b359be92d6c05503c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "19ad3f559f8a8ac66bbf9a697588b5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables2.dart", "hash": "f1f175eff474684786b1b6980f386aca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/pages/cupertino.dart", "hash": "671e5f26fbf94b9d5a70b14c8c494760"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/reference.dart", "hash": "e7a9dcfeea903e16ba7ddc8cc31960d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/pigeon/messages.pigeon.dart", "hash": "27609fef75714481627c2ef33c2eb952"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/tracing.dart", "hash": "565c932b397f73250e40947d5428be7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1generalizedtime.dart", "hash": "1c5e7ae4634ee0e15c9e7a2c4e4bc7aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/socket_io_common.dart", "hash": "0171133a31e915551faadbe90b4bd529"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/grammar.dart", "hash": "467b2b8993363b1b27f034f6c1cca476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_trace_context_header.dart", "hash": "b1fd91732db0b35cbabb5ab6df4a40a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemfilter.dart", "hash": "a9a9ecd14dd90500d067ccb5c564cb22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_mixin.dart", "hash": "5e9d885bc066ae16bcca5bf065c9d51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_tags.dart", "hash": "52b3957b97a8395768c49a8e007d55f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdual.dart", "hash": "75335c9306751e1de52734c1ae433ac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiostreamvolume.dart", "hash": "a88c6c3bfbfabb9924b6b0c3475f45b4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/socket.dart", "hash": "43d850de9058ef14e4de29d5c13c72bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/api.dart", "hash": "c62bc39c4eb449cdc6eca90e1ab97662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/imply_content_type.dart", "hash": "9955b767fdde0baa759d3431267e5ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader.dart", "hash": "0a9121d736af630bee92bd8372e06916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/argon2.dart", "hash": "c048aa4f98f732449005fdd5f36d98ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c789dd4004265224055546db82c4c7c7"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/shared/models/user_model.g.dart", "hash": "103e134fe54b33d6aa090a4eec49fda6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispvoice.dart", "hash": "a47b8729b72b77cd6b5716ed37807a11"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pbkdf2.dart", "hash": "8fc2d5e1cc5209087a568408c74049d6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/aes.dart", "hash": "6aab3b5c1f46dbe2da208d348f8376fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_category.dart", "hash": "a94a67f325606644fee6ad6aa922752e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/debug_print_integration.dart", "hash": "c8126e23b55864058c5dec84373a7dd1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_request.dart", "hash": "53948153a2f1a09c7b54f7f8cab9079b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemresources.dart", "hash": "47eb0e2b093b486abe563cf677b04f31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ibindctx.dart", "hash": "82c3a291bffe63fdad7d6e4bd5b0a0e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/big_int_adapter.dart", "hash": "f962a26b7944264455f9d479c898f535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemclassobject.dart", "hash": "20a078b2eb6ecf6b4b16bd817e30ecdc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp256k1.dart", "hash": "dfb565e96434d9969a6ae0a093e7a40c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "5026d2be7ec0baac217c8ce2c379c9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/span_id.dart", "hash": "d34f762869b82dddc145f7a0415a6a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/micro_money.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_attachment.dart", "hash": "796d0d545778c85ce27a9304092b5ed0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/oleaut32.g.dart", "hash": "d36205839f51ee14bc2d832726c52853"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/hub.dart", "hash": "0227a6a4d217b67999acc0235dd11d4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs7/asn1_encrypted_content_info.dart", "hash": "d4188a33d3cf0948a179a12f37c5a0c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/ctr.dart", "hash": "7cd0c1dd1575b6f4a192990372fd6ef6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/xof_utils.dart", "hash": "2fbe3c5de5fd960b3af2656edc846d07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/lib/method_channel/method_channel_share.dart", "hash": "b956506afe8e95366005ba0607407a2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/date_time_adapter.dart", "hash": "cb28076c9c2d74bd04b62483c2e63193"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_transaction_context.dart", "hash": "30f6bef07277e01956a014e19da190e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/noop_sentry_span.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttoken.dart", "hash": "fb64eb7ccd3a66090cd698eaebe1d080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworks.dart", "hash": "6e3924fcfcaa29ba9146915e7603139c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/state.dart", "hash": "9a453418cc0baa3cf4c4a41655f4a113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_windows.dart", "hash": "6b6d268476b0c6b3d28f6339b57b61b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/dispatcher.dart", "hash": "9de140992b1876855e65cdffbefe8a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/dart_exception_type_identifier_io.dart", "hash": "07849435c4726babb513b4ed9884ad0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "21a6eaa35ec3367210a47a559f54c4a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "15ee18405ccd7752c3035b2f3b86e49f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/native_frames.dart", "hash": "2c06ec3e36aa27caa75538b4860e1d0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ichannelaudiovolume.dart", "hash": "623a5dbc96b4107a93ef35eb90184bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemservices.dart", "hash": "edac48a72d161089af5eee3c0d7d0d5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/misc/error_screen.dart", "hash": "72d27451431aeaf0b4f073a66bacf00f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/public_key_parameter.dart", "hash": "f95ded997d4466677aec73999d61f614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/delegating_list_view_mixin.dart", "hash": "c17576f1b73a93c4effae038a1e2a23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/des_base.dart", "hash": "5dbf70d09e29734788c4dfe7bd9a08bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/renderer/io_renderer.dart", "hash": "238c187fdea02359a91d33d44e8d1772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation.dart", "hash": "fa2fa16f78792d714ca06eb1bbea9db8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_object.dart", "hash": "bc18934a7b1a4820c999a7df4196e282"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/sdk_integration.dart", "hash": "f868411a9074707b91c4e6a623148db8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/transport/transport.dart", "hash": "1b04dc52c5c33c2a8af4bc41e81aa624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/api.dart", "hash": "28b7197a537fe7660eb8cbb1a789a9cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_asn1_encoding_rule_exception.dart", "hash": "1462f888e54609c461c3d74b7314db6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement6.dart", "hash": "92985c94a9a966b97c156c06ab2d5195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_pfx.dart", "hash": "4438a8f51cd1bb37203275181971feca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/environment/environment_variables.dart", "hash": "dd49f951d217fe216b5cc803f65e0bc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecc_fp.dart", "hash": "5c01f919dddeedfac753d5311b6c7f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_encrypted_data.dart", "hash": "9de044db0c3ef6ec8d505dea6c6fd9d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/client_reports/discard_reason.dart", "hash": "44739736a0114175069b11c243e9c948"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_bus_name.dart", "hash": "9cf807e15d1e83af4f62cdeb36582a91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/callback_dispatcher.dart", "hash": "5239ca253366a3b71796f8e9d2baf065"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/pages/custom_transition_page.dart", "hash": "bd81c6cc5eb829742ceb3a955cd852d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/storage_backend.dart", "hash": "60a867309ff4891239672ceeb021e4b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immnotificationclient.dart", "hash": "300a55743890abdcee4f6f0ac897a3d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_writer.dart", "hash": "61da4ed39b7ee4b0a5256d7c7fcd0a61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_response.dart", "hash": "cfac378b4b8d5a5fc9f0a8962996dc1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/LICENSE", "hash": "038c3f869f408e1194eda71cafcca6f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfile.dart", "hash": "9147a0ebdb209d3da9ae7cab703710fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_exception.dart", "hash": "745cb324f6e0bae4e53f0029299d7891"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pkcs5s1_parameter_generator.dart", "hash": "bb37e91f19a10c42b2e8a514f16c3c6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object_manager.dart", "hash": "69c08243f2f74c58d6ad38b17bb5cb9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/cfb.dart", "hash": "18469d4f463892d53ca2c320d7dd6450"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd256.dart", "hash": "ba6e74d916b71ed007cb2126f50074e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumnetworkconnections.dart", "hash": "4e3b785e94de8470e198d0bda80e23bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/sample_rate_format.dart", "hash": "d65f54bf41b9becbbed4f11782b2cb50"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_encoding_rule.dart", "hash": "16b69ff904d37271ee8f4af2b5621f7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imodalwindow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/utils.dart", "hash": "caac146b9d46a9d99ac6d588e06806af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_certification_request.dart", "hash": "1668123cb1d9d7633ffb9ed04693e2f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib/local_auth_android.dart", "hash": "d1014fc9427deb3355db0b126083ed2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_aead_cipher.dart", "hash": "839c5b0bd0d69f5a9b2875f391a1bc9c"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/main.dart", "hash": "935ba2433f96933761c27b3b7b4b1249"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_stack_trace_factory.dart", "hash": "35640d2bfeeaa8a29b0ef065e01a1bc5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/information_provider.dart", "hash": "e0e6a22d50cab6e16266023c58517b54"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/features/profile/providers/profile_provider.dart", "hash": "2c10789b97152cd2419f299ca970885c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/user_interaction/user_interaction_info.dart", "hash": "c7582324c2cb01c5398d1a40a7b6ee54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/performance_collector.dart", "hash": "1500e860f633a60fdac20cc05d034122"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_user_feedback.dart", "hash": "d5c954f2eb80a137833134606952fb87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_block_cipher.dart", "hash": "f0ba8d1c1549b2073eca13fa44f5930c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/background_transformer.dart", "hash": "c3ab437aa0b03081adbfcdff7755b358"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/long_sha2_family_digest.dart", "hash": "6b4b2978d38db2ed3aedb6f0f62d6946"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_server.dart", "hash": "0b4a237293e913152ca376cdcfbe752a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatadispenserex.dart", "hash": "1a8913505e5275e2ead5a2e0752d1ac6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bluetoothapis.g.dart", "hash": "21dfa823454d051c097b62eb7499f71c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute.dart", "hash": "12b8cbac25c7ad95ce53c2f8869a1b5d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/mechanism.dart", "hash": "54a80676d9ed863c3bfc43f5a31b8b59"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry.dart", "hash": "a993ca2b8748f43da4eb160b12a15718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/screenshot_integration.dart", "hash": "4b835f586a424856e724617a7068417e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumstring.dart", "hash": "68e28643e39694f628939978acdc52f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/client_reports/noop_client_report_recorder.dart", "hash": "9d652d03448bc9c7b25d104727d31c32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/sentry_http_client_error.dart", "hash": "4a3f65cd5f29fb80ad375535c716aed8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/hkdf.dart", "hash": "7f593a221d1320305917631f622a5ba1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/default_style_information.dart", "hash": "4cc8128599d4dfdcbd699b3f01d68904"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/helpers.dart", "hash": "25feac2cd9c96cc475403e601757cdaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllinkdatalist.dart", "hash": "a82741847c5177c47adfd428a1583744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/errors.dart", "hash": "246aa94bb2d86ce16c59b69b13d42904"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/frame_helper.dart", "hash": "cb79a30b4326b1cbfb62680949394769"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_key.dart", "hash": "b4b62a6be8e303f9d9a5b5408bf9106c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/exceptions.dart", "hash": "0400c53ca2e9230b51a6f361146dee28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/src/messages.g.dart", "hash": "f1c7d23cd6db9504510e67e2957b4aef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataimport2.dart", "hash": "cb23738bdb6f2e8319ba8e9dac0072ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/idispatch.dart", "hash": "8ef246eaf180b7621f716282e295c950"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/transport_utils.dart", "hash": "d35d32c833220736594c4d2af54c6a89"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/src/messages.g.dart", "hash": "43f414cb7bdf200801888b9a62c38f7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_asymmetric_block_cipher.dart", "hash": "feeb432aa90175168f140209d26a4e83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_linux.dart", "hash": "153e569a429470f19962e80723cbf73f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/eax.dart", "hash": "db3d47bbd056cc10e133a00439925906"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/enricher/io_enricher_event_processor.dart", "hash": "432db3351d2240eb7d6e4726da575ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/api.dart", "hash": "9c1b891920008b53059d7791d88f7d4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio/dio_for_native.dart", "hash": "6f053637ded96c67b342e6a80e7372f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/device_info_plus_platform_interface.dart", "hash": "4b532e7a43e7a2a180a49c5869ec8ab4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "hash": "dcb1bf21d8afb364e20a47f106496780"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/firebase_core_platform_interface.dart", "hash": "a12b9a0771829ebdd5571928f9c48e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "********************************"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/features/trading/providers/trading_provider.dart", "hash": "6938285427d0b1c17f43063294fae8f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lookup.dart", "hash": "b76abc07781824bc4c06d11631188db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/hive_extensions.dart", "hash": "3a5e5ce96980d4eeb6ef4992080817d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols_data.dart", "hash": "f176d4d0e0b6d9e454dc1b0f0498507a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersiststream.dart", "hash": "ba4b050fb9bed64eb6f6476016aeba2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/location_database.dart", "hash": "011e1e9f46dfe9400619c8e5103c30ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/_internal.dart", "hash": "ef4618b5bf737a7625f62d841127c69d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive_error.dart", "hash": "705c71a4fde7fd9f2f8130b35b98caa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/scheduled_recorder_config.dart", "hash": "09bb5348b3804c1b9bd0b57a5af96fcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/remote_message.dart", "hash": "f4b52208f2ac65794c0722ae22b2ed5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/rsa_key_generator.dart", "hash": "c5ecf74912c76ddf0eb32a599ce4ce0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient2.dart", "hash": "48f954e66b945620e43ce8e9a8891919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid_windows.dart", "hash": "659cff14f1665a31dec63407d7839624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumidlist.dart", "hash": "7d1806cb19bc0d23a18c2760d106d95e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/platform_check/native.dart", "hash": "a4a5fbf032ba354e38fac051df152909"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_ia5_string.dart", "hash": "0edffacf0034337f57cd291eb488a52a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v2.dart", "hash": "5c05c15d14b799cb005474b4676d8fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/url_details.dart", "hash": "d0f5b844df029dea3a3cbb79441f2f1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_flutter_local_notifications.dart", "hash": "128d5c1f866dde97fe9cf1c85f2bcb27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/notification_channel_group.dart", "hash": "9a2704474807a196e3a72883d73b5be2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/path_utils.dart", "hash": "228413c644cb471229965818da205c6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/auth_messages.dart", "hash": "f36296a5403e8c6265b437c034411f50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/parser/is_binary.dart", "hash": "178cc9aafc835550ec702354f598734c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/enricher/flutter_runtime.dart", "hash": "09f41bb6d43209b96f9fff213fd684e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp224t1.dart", "hash": "8313696f70c1a0137e43aacbbf23a76f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_adapter.dart", "hash": "ed743446165700520a88ccc56514877d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/scrypt.dart", "hash": "0d695a7dfb1bdede54b449bc703d346d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp128r2.dart", "hash": "b169ef1943f4cc4ec4d8f5c56b25a622"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_type.dart", "hash": "f5e211d8beeb6fe549de90fbc48a4a35"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "00bfa437eaf641f6fdf0db2367135a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanager.dart", "hash": "9915c7d7ab3c9994e77dc4abfba9418d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_remote_object.dart", "hash": "4f187fc37cb2a7eedf4681e2321792f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_list.dart", "hash": "be45023218a3803531ceb7521533bf9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/lazy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp256r1.dart", "hash": "87c7f992b5cfb59a98477aeca4399c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/xinput1_4.g.dart", "hash": "08b6eae008bb8359796643eb1a639234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/exception_cause_extractor.dart", "hash": "e29a482c5a9807cc9cad60b0902cab2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/ios_device_info.dart", "hash": "2ac6c71ca1b3241eec36c619a2db2c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/_flutterfire_internals.dart", "hash": "170a9dec4d2bc2ecead5894266edcd0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/consolidate_bytes.dart", "hash": "b4446a7a4d053aaa35a7bc6968b4794a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/connectivity/connectivity_integration.dart", "hash": "419433cbdce77f193d8ecd7916ed13c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation5.dart", "hash": "d879c3156e19f2b290c4d6eed1de5e89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "35abc3f166f0485c87a21f0fcecae69a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ufixnum.dart", "hash": "efa0f0bfadfa23e80d42d74b41704530"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/platform_exception_event_processor.dart", "hash": "f58c149542a5eaf6f952ccd92c19d18b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/parseqs.dart", "hash": "79bc31f6208de22a4e53553d0a1c56ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/integration.dart", "hash": "077740735d37932960ac37e1b8161db9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/notification_details.dart", "hash": "5bc24b31455e76bc74c05a2ee528dcbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_salt_configuration.dart", "hash": "59fad971cfc70efd0d2fe9df45863abe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "d5e0952742a6404c71b939292023e2cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha7539.dart", "hash": "52c8a27d05b6c19fb1478aee4c69a6d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/rometadata.g.dart", "hash": "87ac4b62f17065d7456bfb6f6ec0a624"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_parser.dart", "hash": "d75b4941ea5e46e3ecb8ce8f22847817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/route_data.dart", "hash": "8fdbe6c284b56683447368dde52806c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/storage.dart", "hash": "3032f1c2edfd44ab46f3b4673c5c8deb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "15639b799e4dbb06ffd538d943964d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immdevice.dart", "hash": "b5e211d1bb1c533a77b5638eede5479f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/exception/exception_event_processor.dart", "hash": "42c7826293c8772ee6b6503edbcb34e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_envelope_item_header.dart", "hash": "308cca8b0bc001d6e27132e73029c4c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/.env", "hash": "08ae357f65d127c7850a0c669619b45d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/md4_family_digest.dart", "hash": "024f7bd028389820eceb3c964a114141"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/LICENSE", "hash": "35889fcd28be68a600d7f4ff44c4388e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/c/utils.dart", "hash": "37b4afad4aeeb7df464d8d1f4ea000b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_object.dart", "hash": "08b848f81523e9f11afbad3153f6dac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_category_option.dart", "hash": "f328cfe04255be8a4d740b54f2854bbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/isolate_error_integration.dart", "hash": "4ce58625d709ca5d21fc1e4ec1f8f372"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/package_config_subset", "hash": "ad986ffc1850e10ab1190ca0c0744907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/span_status.dart", "hash": "87ea487272f3d07ea09f990843c3f207"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_cipher.dart", "hash": "3cfbf0fce1ba4d19780f691ac1ceff76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/char.dart", "hash": "00456c7fcfc11e9ae46af126277652d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object.dart", "hash": "0cb51131f14d4d8df95aee83e4931780"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemcontext.dart", "hash": "ecca8d7a94b7a01ee70af109474706b4"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/b01bf0d1026b477a0a2df2842c7e9f2d/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader5.dart", "hash": "85574281bf7d7bee9722a21e092b4be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/transport/io_websocket_transport.dart", "hash": "70faf5bdb5be5d5b281150ca8d422683"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/run_event_processors.dart", "hash": "00745dbbae16624942efdd3a9e1c70bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/LICENSE", "hash": "22751c3fbc8668904249121c47d419af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/enricher/enricher_event_processor.dart", "hash": "b2b47479724c266bc99b284985042a8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp192r1.dart", "hash": "ff3b9eeee0120faa24ef23172fc5bacb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/manager.dart", "hash": "477494573a5b93fa177df3e7c60dc431"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_view_hierarchy_element.dart", "hash": "140f385f3fa22698fb0011cc6b6bab34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/setupapi.g.dart", "hash": "9d0390331a2be3f7c018dab8bfa589de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/redirect_record.dart", "hash": "91794c215a8aa39b862cfa4c96b9a398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_measurement_unit.dart", "hash": "8f869d4368c8d725543b0cd4378bb7dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/firebase_exception.dart", "hash": "7cb7fe22378ec39b40d4b519d0928d80"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE", "hash": "abb5a1fdfd2511538e3e70557aad0ba1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_span_interface.dart", "hash": "1f7ce49a2350b584a35cfffe931c3305"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/configuration.dart", "hash": "1ca6e7022b3a950a205e316f7e3a2b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "21ee375f5cb7acd3bec0129fba2839ca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/factory_real.dart", "hash": "e328c5214945f38496f8500e354a1ab0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches.dart", "hash": "3459a8a962662899e6d1ed009af8ba45"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/mac.dart", "hash": "de2ea3d1fa63703972769a6dd0c0bce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "hash": "0902c41eed709a7841f11130fac2a593"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_object_identifier.dart", "hash": "06bd3bc262afab597464a920ebe98032"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dwmapi.g.dart", "hash": "20290eb1c157dcb3947d9e5b420ea50e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/src/typedefs.dart", "hash": "3e93222dc359a938c1354ba486d44244"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/big_picture_style_information.dart", "hash": "5f8bbfd23974ae2842d3d03760b98f99"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padded_block_cipher_parameters.dart", "hash": "b5c7af5d480c2e01c449b0be049b20cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialogcustomize.dart", "hash": "859de35a02fbe705941f97e7700a3147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/noop_hub.dart", "hash": "1d8cf9f6d57a475dbf5eb4405963ec14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/indexable_skip_list.dart", "hash": "eda351b39b4854648a4d265ed1605fcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sdk_version.dart", "hash": "f4e62769430bc87368bd2544415b8b25"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/date_symbols.dart", "hash": "4c94c1ae460dd53255786f0ce3b53463"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "77fe24649991a149ec3886147da46e40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "7d8e8a43fd286d637f95a0510b0d3c84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/client_provider.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "0c4386f8def5b3a82bf0b70090830314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/encrypted.dart", "hash": "f226d8b0fbba304ed6bbca081a613971"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "a0c11bb2957ee28a1de2145cc233367d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/flutter_local_notifications_plugin.dart", "hash": "e4df671135a9faedffb36e92e0fad4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_peer.dart", "hash": "681b70272ec68e757f2394c9e7fa9398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/c/sentry_native.dart", "hash": "704a65e4f572602611b429f14e7c6ce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid.dart", "hash": "49d6d829ae481b2570a290401389d149"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_base.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/macros.dart", "hash": "8016baf49ccbce205455e3fc0bddbb17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_object_internal.dart", "hash": "1d6b06c440ce770d590ccc694f67e7de"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "3d63c4213a898f6e0eb52cb39fa282ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_linux.dart", "hash": "c273c5105a2ca17896243db15f9b07ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "a3ab60b19b4725b3ea1d1b0cb1c64451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/parameter.dart", "hash": "08b1358e505b0414dc60489b750ba2b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/method_channel/utils/exception.dart", "hash": "8abcbb724ffa31d2cf158a95c588db62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "aef722a64f462b84d30dad6278040fb4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/settable.dart", "hash": "f5f653af8a150de004f1b3ca1633bceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/magic_number.dart", "hash": "d9d40cd4fd7e692ca4246d952d48cca8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/mime.dart", "hash": "90fb56de843f1f933c2ba8ec945fa06f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl_helpers.dart", "hash": "fac5ee1098b41fef8637aca152781c92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/propsys.g.dart", "hash": "c226787e49d4779d8fd575f9bf26e298"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiorenderclient.dart", "hash": "64708122ad6cca0c23373706cdb72c03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/navigation/time_to_display_tracker.dart", "hash": "4da80f3d93f538cd985e5ae35f0a132e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hex-0.2.0/LICENSE", "hash": "2fcd51d6c8661a5d975faac0c6459f70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/timezone.dart", "hash": "f8c5df6155feb71c22fdca5ea2d10a53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v3.dart", "hash": "8d69b0d7e796398d342532e8a90521ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/range.dart", "hash": "a6e57cd7b87262b784eb2efe6875a329"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1exception.dart", "hash": "b1e77e9e7b9fcf421147af98dbf67760"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/where.dart", "hash": "bab2294ec70ff137aca684dd19203943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/annotations/hive_type.dart", "hash": "b26d0a2e3e209b52ffb697f829ec46cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/models.dart", "hash": "792752250b7a3d3bdb27d7616c40819a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxfactory.dart", "hash": "93d835e43f33ca5ed96e6e85a392c1e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker2.dart", "hash": "03b20b9fede21601f0b3d0f7ef4ce25f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "942a7879522bdf82258a3383893665a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v1.dart", "hash": "78f28534085a9036071a8642c131a8cb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-5.2.3/LICENSE", "hash": "b37440f2e508d0e6666a3f6796a03ecc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_uuid.dart", "hash": "c9efc107e2b16a48d4e132bfcc679af4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "0502dbd75b5b023cd08bf81003a77889"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file.dart", "hash": "4b7bd97845d5fc94f590ed6e58f1c1c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/fused_transformer.dart", "hash": "4cbacf46dc43afb0d059b0016010f45b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/propertykey.dart", "hash": "241ccb24efad22e002bdfe778f96b46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/method_channel_package_info.dart", "hash": "5489bd1170add17f6d3bcc248b5ed048"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.env", "hash": "08ae357f65d127c7850a0c669619b45d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/scheduled_recorder.dart", "hash": "9bb5ee05942408acece3a9ca4146824d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key_pair.dart", "hash": "2d62ac6ca4d4859bcf344bb57579b632"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/argon2_native_int_impl.dart", "hash": "066e5bc376b697bd725a5520cb26b194"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ofb.dart", "hash": "78e2885cda7b8ebe22bf138d66086847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "hash": "3d18e1306d78e114f98c9dc311fbf158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols.dart", "hash": "6c1b7903629a7ad4cb985f0898953db1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/profiling.dart", "hash": "2c829b6cacc339c8fd121b9bbf43c5e0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/hint.dart", "hash": "570573fffe43860513d5cc911da0668f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/icon.dart", "hash": "b1d3d657c21d4c2229511410eb2240c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/LICENSE", "hash": "6c189f90a66a3490dde93cebc211f9c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd160.dart", "hash": "946665f8d69b449931369c446519f3b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/pbe_parameters_generator.dart", "hash": "a93d576823e58041b99309fd23a1d547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/src/network_manager_client.dart", "hash": "60838abe37c945cf06c1b5ccc5066fed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bip39-1.0.6/LICENSE", "hash": "053e3cae6e0b030501e60fd339280272"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/deduplication_event_processor.dart", "hash": "11c0f0deb37119fb10ad3c9ed6c78d5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/device_info_plus.dart", "hash": "33970ebf975bcd8cde1fa7156460e38d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/http_transport.dart", "hash": "fd168df729680cd160469102ad79fa83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/theme/app_theme.dart", "hash": "bed4527c21dc5d279e5de9e532a68c68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/scheduler.dart", "hash": "2f63e230c7e48a0b125c48a9f0b4da9b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_encrypted_private_key_info.dart", "hash": "df97742fe1045d91a738194f3f00fa48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/object_identifiers.dart", "hash": "b1218847a912b31d0ce265943e8f6a20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellchecker.dart", "hash": "556c5677ab197ac52aaee6e02d6ebd70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/screenshot.dart", "hash": "5ded68df03a20f7e7184bfb4229bb188"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/rate_limiter.dart", "hash": "39ff31de2570cecde6d68c2e8bdd6b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/pkcs1.dart", "hash": "34cf46d3d1d6fe648207862c6e4aae80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/sentry_native_invoker.dart", "hash": "478f25bba656636ecadfd0f1482407ec"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1application.dart", "hash": "10e51c3546a0a1a57a4e54478af9514e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "2ee116ca87b7e1c461de1462c3442ec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/run_zoned_guarded_integration.dart", "hash": "66e95a7e8b95f6ee3c683e6244c3116c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/gctr.dart", "hash": "31e1c73ec1091931223281021493c8b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/cipher_parameters.dart", "hash": "7584e95f9a7adfa3131ea1bbe7d81088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "10ee0ac3bc045cf4344c623f4396d941"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "4f4a2d291e23c96c7ae0d4dbc9598c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/LICENSE", "hash": "eec8df566dadd4c873d9e323a7e31bd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/lib/share_plus_platform_interface.dart", "hash": "7e38424729d139f4ac1ff5183cd59303"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation4.dart", "hash": "d8b980603638367071e1f1c256ebd56f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/rate_limit.dart", "hash": "aa045a506e1ec79eaf74bc026e62ffc4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/utils.dart", "hash": "599be812b0d48a34af027e2c896771e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp256r1.dart", "hash": "9af42975a39b3169fa2a3ff5bdf5aea3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/src/firebase.dart", "hash": "01049ee9adcd36bdba77ebdbf08c8311"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/fernet.dart", "hash": "844e459e0d8ee6db01c284160ba99ccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_enabled_options.dart", "hash": "877295d0c356a690a3b16d271e34c543"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/pointycastle.dart", "hash": "456516d7456834b1833931118899104f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "437b5795f5b9bf507b02ed5d44f9f572"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/aes_fast.dart", "hash": "052847524e2484cdfad41d0cca0904e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_name.dart", "hash": "80e54c212b5553089f84e0239fb6c570"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_action.dart", "hash": "6a3849c802c2fd63cd4d3db06470f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "hash": "815ca599c9df247a0c7f619bab123dad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/device_info_plus_windows.dart", "hash": "9649ae3abaa2347c68833b467110ccf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/recursive_exception_cause_extractor.dart", "hash": "a334af2ef1a17cc5160e2f25090a7f71"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/utils/timer_debouncer.dart", "hash": "5cb1c7e04dfcd126957e5da3844b740d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/method_channel/method_channel_firebase_app.dart", "hash": "4f4575a514eec25990a9923547e2ac28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comdlg32.g.dart", "hash": "cd103a8b0a9727840f3bd8bd985ad677"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/backend_manager.dart", "hash": "c1320c369344322829e5b7c8d63e0d58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/registry/registration.dart", "hash": "75cbf5c83dd2ed0ab6067971eddb1829"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/bthprops.g.dart", "hash": "0b9138f9bd3068b518494cfee8627cec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "d7be13ee7803d293bd92452e5ef3da27"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/api.dart", "hash": "72b70b9814ae3e83df7a4c09ac2780d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/dart_exception_type_identifier.dart", "hash": "885821df46a83dc816f7f1c7b6fdb14e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1utf8string.dart", "hash": "51d920b8517f9552fbc53355b0aedde1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wallet-0.0.13/LICENSE", "hash": "c4ca5eba5c5dcf1c9365d8310fbd88a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/compact_number_format.dart", "hash": "9068f4d63af1ec44245b76b7ab4dfa48"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_path_l1_1_0.g.dart", "hash": "42efc7a615bdc348ad78098c1cdb79b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/sentry.dart", "hash": "efc5af0f3c1366cdcc0ca944560a666b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/default_key_comparator.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/irestrictederrorinfo.dart", "hash": "a42121307a3d24f06691ab35f935206a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/media_style_information.dart", "hash": "4fe97d87eee37e8a1dddc5230ebbf9ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelementarray.dart", "hash": "e7ee3c364551618835ecb4e3afe065ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/poly1305.dart", "hash": "adad7c45c2cfb5082b4bd61830653235"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_padding.dart", "hash": "dae7ae92007717d576679bd62bb2ce47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/salsa20.dart", "hash": "7b2925b690ec9bfbb94a651070da76f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_registry_impl.dart", "hash": "74bcfa36a4954c05f1b8a9d5ed663c8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/diagnostic_logger.dart", "hash": "99e82ecbdf8d8243f07589c75f89f207"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/crc32.dart", "hash": "21913fbf147ca790e444082cf32a7c84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_message.dart", "hash": "eb54a5ead5cb8ea548f36e4b8780e4b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE", "hash": "80ae6870ab712d32cc9dff7f6174b603"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/file_picker.dart", "hash": "9b8d127059badd7589277aea7122e4ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_writer_impl.dart", "hash": "7f3d8ecd3382ba1196fa6ede8b4c8fe8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement9.dart", "hash": "13e53604d98eb0a2fbd871588ec8b357"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/debug_meta.dart", "hash": "9d6b5cd738a155717f8581e16ca66a25"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/method_channel/method_channel_messaging.dart", "hash": "4c038e0b7740e9569259de4eaf7341d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser_base.dart", "hash": "39348131fc86fb08a42dd6b2d1b16bf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/sync_transformer.dart", "hash": "787074c3d370e068052721d16acefd9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/file_system.dart", "hash": "f72f7c9e3a3971fdfd58d38c94b4e005"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_base_impl.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/connectivity/connectivity_provider.dart", "hash": "fcc48b085ff1c49bc2db83a793f098bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxpackagereader.dart", "hash": "59137da0b55aefe8a4074891792a55b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/pages/material.dart", "hash": "61f9ae17975d4d233db25ee3f27633bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi.dart", "hash": "68634d4df864077f507d84d92953a99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/storage_backend_vm.dart", "hash": "29255b18bbbac9298fb8d4964f6610eb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/firebase_messaging_platform_interface.dart", "hash": "b259b177c1515fa5820f8213e12fa75e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "6905ddd5343384c6898473c3d0a553a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/transport/io_transports.dart", "hash": "bf70e4fc7ea6bc1aae24c40001552410"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_octet_string.dart", "hash": "4712bf9834e41f575c82673a4c91c279"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/powrprof.g.dart", "hash": "bbfc82fc5cadc3b055adeaa481dfee0d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_widget.dart", "hash": "0709c21c68894edf57ea88158781f4f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_manager.dart", "hash": "5f173a5c0de15909e95d3275051138c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_multipart_transformer.dart", "hash": "531d1d96bce7aa59a6109c02ac538cb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_parameters.dart", "hash": "e3eb86ef252b151d52688c8904935b3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog.dart", "hash": "8a251fb90302207f7e9e3f95aca01a72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/socket.dart", "hash": "ffe6e9205b3e76b722f1b6ec779e5c89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "405ff2b0c110ef10a33e496bf7db38a1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "aae059b82ff751f6e81487ef98668661"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/buffered_file_reader.dart", "hash": "4debbc32ca311b2ac8a215350cc72fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/view_hierarchy/sentry_tree_walker.dart", "hash": "8f9ca037df30ed597f7fc74241693f52"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/src/types.dart", "hash": "4a9817f509bb8eb7192a89fa9aa015dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp160t1.dart", "hash": "eb845be8c283dbdc0d511ab93679d402"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd128.dart", "hash": "77233d53e3db6b1f94881a6e822dd168"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/file_picker_result.dart", "hash": "592fbd715117ae860b51b29d0be0bd50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/scarddlg.g.dart", "hash": "ff51e95e52fd4d789e71223942d5ae23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/enums.dart", "hash": "523742c594766cc9e39179d93cb23259"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1ipaddress.dart", "hash": "30d691e618930682703db00c4058bb8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetwork.dart", "hash": "57adb1ac7ff40f2fd9512ebf09281433"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp256t1.dart", "hash": "545a2816c908e3ed8595fa9d883cfe89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/signature.dart", "hash": "1e8c55180c1ddf0d1a142eafbd3536f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipropertystore.dart", "hash": "de49c234a47c24f91be2f223476fcd44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/version.g.dart", "hash": "08a0131d87ba3b2535a2de787086a3d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/image_picker_android.dart", "hash": "007c2b99a7ab8b0ea0ed298ac83d52b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/client_report_transport.dart", "hash": "389d2f4d720331450e412e1858d5d1e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "a5c23bf569325f140ab7b7d88d3b683a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "hash": "88d5feb6f0a1ddf0cafe75a071bbcab2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "5764fde6a5cfb0402dca339562afb9cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/public_key.dart", "hash": "adfd4b4c9f127d6d7b0278d25c16cf75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "ebd18139757966fd0be670b4af2c028a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/platform/_io_platform.dart", "hash": "0698bdf8cacbee659769363ce8b0d8e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_key_derivator.dart", "hash": "15503bd3441ce266624f6509e5a7d6e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/environment/_web_environment_variables.dart", "hash": "fa1a65a37805aef2fbd0b9a17365cd0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock2.dart", "hash": "286726a4ae635c3cb149cd640c3c096f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp128r1.dart", "hash": "bb0e9102c94c06da15d291c296ae6d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/winmm.g.dart", "hash": "34b9072869b35b15ccbe1d843fa778d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishelllink.dart", "hash": "7132bdf47eb7567294754da6caddbe14"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/style_information.dart", "hash": "9787d9b12ea9461874ea0faa9cccf9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/parser/parser.dart", "hash": "060e0107db5fa47058a5507e32f1e18b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_stream_cipher.dart", "hash": "595e228e65be42abc3a612b741bb541d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/android_platform_exception_event_processor.dart", "hash": "89601eba4e3fd6df679aa676cb619595"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/cocoa/binding.dart", "hash": "c509993a25d09b268ef78913cb82f517"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs1/asn1_digest_info.dart", "hash": "11a61d462d75bfb40a3e011486a042b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1parser.dart", "hash": "35289b444f8aee514f82056062db704e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_key_bag.dart", "hash": "6c8b0130bb90390a5219ecde2cda29c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/types/auth_messages_ios.dart", "hash": "84f8da617ffbe5c187ab3340e6d17cce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/LICENSE", "hash": "a27f596192906ba7cf9b6375cef4573a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/packages/material_design_icons_flutter/lib/fonts/materialdesignicons-webfont.ttf", "hash": "4192c96835815b160f03a005cabed284"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/isolate_utils.dart", "hash": "a3f7ea29025b3f46d6d054a9a5f5ef0b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/package_info_plus.dart", "hash": "41af983ad4476c4b4efac50009fe3691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumwbemclassobject.dart", "hash": "17399c5876a7f1c340f8814cbc903b10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/initialization_settings.dart", "hash": "150f91352c1070fd5f15a65ba10e9cda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/LICENSE", "hash": "3dcd6c4e98c9df524fe39c0c979a5300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerfactory.dart", "hash": "3aa843b290b927ec2ae60e30f12b4ab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/magnification.g.dart", "hash": "c63a357184bab34ab1e8522808a9cdf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "dfa5338b5b93f9705e9f756dc0327549"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "6a38c376b8edbead42348c54f9f12420"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp521r1.dart", "hash": "2ed0d22935079b89bbfcb6141b37999e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/posix.dart", "hash": "f19239fe10cca0cd002c22edba90eb52"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/load_release_integration.dart", "hash": "54ec557fc5096c654c3ebe08ef93ccdc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/constant.dart", "hash": "84391293163d781c7715a32ce43b3c7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format.dart", "hash": "20dc50b53035a8e953b5d4ffe6948ede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/choice.dart", "hash": "253b43ba9075c77f9ce5267d91880da6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "3796ca959ef2c6e4bfd668640a318ad1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/sound.dart", "hash": "58f14973ee61401b0bf79de491dd1e69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "62710fd39bf51f264c7fd8ad1dc7aac5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersistfile.dart", "hash": "0f1d84a9023a931b4b3cda6b907d76e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/gcm.dart", "hash": "bb64ec5f608bf82d3c275c3978893381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/src/messages.g.dart", "hash": "eb1665fb174bee1200143c40c872b1ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/widget_event_processor.dart", "hash": "16ce62130ac7eb962f5da1a712ce9af9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/models/registry_hive.dart", "hash": "6ae62d29deefd524cb417afb7b9794bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/rate_limit_parser.dart", "hash": "b6014303c829d3d4b0bcf1b58d3fa494"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_utc_time.dart", "hash": "cf8bc581794031043fe663a10b541530"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "c9d6f3ad91eab20616b7f28bc6d83851"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/desede_engine.dart", "hash": "c81da903482180f19bfa397e4020bd52"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/load_dart_debug_images_integration.dart", "hash": "58fafd7ca916ec6dce41271402c262b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/renderer/renderer.dart", "hash": "b0ad71aeef2e46743ed538a877b19859"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/srp_server.dart", "hash": "3e4b026bb9c6dfce5a669a3ee88c0a7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadatatables.dart", "hash": "02b96169889bac260344fa44343235e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_privacy_options.dart", "hash": "94a0f56e61f074915f0ec1dcc8b0ba67"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/salsa20.dart", "hash": "c08888c73393c2a5803d8d53ad303835"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_data.dart", "hash": "e1e3a7882488820f09a44a49d8efed8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/uxtheme.g.dart", "hash": "14ca92a49cc066f7dbf04357098fef9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/export.dart", "hash": "18b06ed8e19ec8469c043edec467ebc7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response/response_stream_handler.dart", "hash": "87061e866d20d4a66d6990c36638681f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/linux/qarma_and_zenity_handler.dart", "hash": "774cc34b6516c76327fe70e51e036364"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptor.dart", "hash": "9c6333c301963385de32595f9442d6ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_attribute.dart", "hash": "666073cafbc9e0c03a3939b99ec35aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/LICENSE", "hash": "a02789da8b51e7b039db4810ec3a7d03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_replay_options.dart", "hash": "c52fbb68f6de38b87327fbfe593ee4ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "hash": "49286617067167600a8c7357dff1dcfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/local_auth_windows.dart", "hash": "0aeba1cff194006a9d8e502e435d4730"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/rsa_signer.dart", "hash": "f3c830efdb17a1726ac1af2ec003ef61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1ia5string.dart", "hash": "ff177b23419bdd4e25448562c040a8c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/LICENSE", "hash": "ef2f4f5048c86bfd71a39175b6f103d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechvoice.dart", "hash": "38d7929920e46438585ed549abb3690a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format_parser.dart", "hash": "61a0deef2a4f0ebaed506bb2a22c5185"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/adapters/stream_cipher_as_block_cipher.dart", "hash": "e9c8e087c6a13fa13c36504a1228859b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/string.dart", "hash": "be2e3e8ab6ed0e2b2b554a26b78f91f0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/web_browser_info.dart", "hash": "9e887cddbdf6c6c7c650a995832b127f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/tz_datetime_mapper.dart", "hash": "2f6d6663f131dd0e24f37f58530342c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/breadcrumb.dart", "hash": "22bffb325564b2b92ab9662f61c604d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_aead_block_cipher.dart", "hash": "45b660175c00f44ef94fd58bd294258f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp512t1.dart", "hash": "79edf39a6bc9b256fa40cf91ac34c08f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/cocoa/sentry_native_cocoa.dart", "hash": "ac877c561e07058caf613946b7e417e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format_field.dart", "hash": "53b1a2074650b8f2808e620e2b9ddc41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v3.dart", "hash": "d85a6c479a410094f7ee24bd2084b481"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v1.dart", "hash": "3ababf685f66cd8d5b13ed724d607809"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/ignored_type_adapter.dart", "hash": "b2ffb1a4d0254b77d2b63bfa6920223e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecdh.dart", "hash": "da5bab9eb0ad94cdf2fec0ada2a8c2ff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp384r1.dart", "hash": "28e04090f5c27de03d786a00eb52fb2f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/storage_backend_memory.dart", "hash": "a8833e6afcfa9f667d78607fb38747ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/platform_check/platform_check.dart", "hash": "c5036d9517a6d4cf0bcd2a978c85df6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x509/asn1_algorithm_identifier.dart", "hash": "2e28d4e67e9696c085e54b54aef9394d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformer.dart", "hash": "49dba21de16234aaed28f8fd898543a7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1null.dart", "hash": "db5ad11249ecd359da07740652abc5cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/lazy.dart", "hash": "128e022b683572b60bce0c93cd05007c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "8d6e1950b64962d48ef050d9517791be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/fonts/materialdesignicons-webfont.ttf", "hash": "4192c96835815b160f03a005cabed284"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/paddings/pkcs7.dart", "hash": "8c54825eb6ecf25c79adfb0d890858bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/cocoa/cocoa_replay_recorder.dart", "hash": "34ee0c9f92e4acee1ed286e8b1e62cc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/entropy.dart", "hash": "6e92b2107eef3d9fd4ceace7819a66f1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_level.dart", "hash": "4c243a6ca83ee01bb17db0d0a77c681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/flutter_error_integration.dart", "hash": "b524306f05de95533617c3df8a5bdd65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "c81713fc58f35111f30b5ef09b79cef5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/stream_cipher.dart", "hash": "625e237fb6c6be7fbd30f31b9c6793fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1bmpstring.dart", "hash": "6947916ae1dbdc54f4474073e00d3679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechaudioformat.dart", "hash": "f7b5a54fb6f6b69cc4234a97ce7977e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/types.dart", "hash": "24b206328a01c6923f0c599c64088645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_transaction_info.dart", "hash": "92e6a86f15bda71fbae8cf4cbf248f06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format.dart", "hash": "76052188e777d0ca03128d3b299d836c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/event_processor/exception/io_exception_event_processor.dart", "hash": "81bb09fc4c546dace1b0c68526c9efed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/parser.dart", "hash": "14f50d28cd925e630a3c1b8205fff395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/navigation/time_to_initial_display_tracker.dart", "hash": "534ee7a8d58fc060aff5c5726f1005a0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "90879288f848e0c8bd3e17539633770a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/default_compaction_strategy.dart", "hash": "32ef2d2128b50f494da6ea7571d1f7f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/version.dart", "hash": "c126373a3d23c15ea14a6fe7cc85a6ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/file_picker_io.dart", "hash": "3f00f5167a03a0497f354bcd33b13bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/inline.dart", "hash": "7cfb88f7da0c2022734fb4c438317d95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_server.dart", "hash": "8580846ee9612281791cc377a99d0581"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "112daf1e5c2a46f4b457e3b76cf569ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/rsa.dart", "hash": "4c852dd5fa9ed3fc4d85a1582632f568"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/possessive.dart", "hash": "08e17247b131fb75466c336e9a11fcfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellfolder.dart", "hash": "9595cf0e7becb4bf5de5d3a3865d631d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1octetstring.dart", "hash": "dbb97fd48268228ae8d8952d0779863e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "hash": "87ee25bbef5b7cb7dcb056c3ec20f243"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader3.dart", "hash": "e97932f0cef53e2c018203ac3cf1c7e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.17.5/LICENSE", "hash": "2abd2c9a42d4caf2b4f1640d68b02fd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_block_cipher.dart", "hash": "93ccd61c35577ea95e16ebca0c058494"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shlwapi.g.dart", "hash": "4230059d9b32165301d8d2a329a9b40d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/parser/binary.dart", "hash": "00dd216056b5a9ff9541e9e01d3d3df3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/digest.dart", "hash": "e660b23c1ae29f74711126be6e560bf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padding.dart", "hash": "c9133480cc3efe535d32fe91abaadc6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE", "hash": "7b2b74124c0fcf7df0c0b4605f9cac61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_transaction.dart", "hash": "21a8e610e139816a3487cdd7732f64f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1boolean.dart", "hash": "e4ad456a21e3a2abf87b2fcaf963d6ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "ff5c40ddc1501e3be7aa7efd4a269f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "hash": "4a9b1f00f6665e425a008a2201361658"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol.dart", "hash": "d5394fdc46a7b592bb8678638f839875"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/production_filter.dart", "hash": "d455a0ea71515758776153cc65cb1978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_message.dart", "hash": "4ec782e81abc8fa70a654881e12a4052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement4.dart", "hash": "98e80e3c681156f330d79925f2675eb2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapters/io_adapter.dart", "hash": "b7579897a220a029c3ea36d6d48b4144"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/bstr.dart", "hash": "0ace55de06ef5d40b277ac8dae4d760d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/_io_get_isolate_name.dart", "hash": "295cdb23ca73d107279fd41583eac636"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_level.dart", "hash": "9676f9bde24c1d9a0498cf2337cd9ee7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/mappers.dart", "hash": "27bd31140f6d692c98f0cc901a7d77a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/capabilities.dart", "hash": "b7729342f9613bd823c71f9c12c680b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/match.dart", "hash": "8b5af408fdaeb769969e75d843cf424b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web3dart-2.7.3/LICENSE", "hash": "975d7513425f2fb0eae88b3f34bdcfef"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "e821a3393869c332a5853ddd12bfbdc8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/initialization_settings.dart", "hash": "84dbd0aa38844da5b7a683c754582df9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_cert_bag.dart", "hash": "50ea93c36c3536adb8c3831a5ff255fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md2.dart", "hash": "5994aca748b376c7ba9e02b2344c61ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/definition.dart", "hash": "8680f57e6ae9665a5f051c06c1efc688"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/go_router.dart", "hash": "94124aa8c115b3bc8553ba80c419ceeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/local_auth_platform_interface.dart", "hash": "cbdfe985d2a7b78662d58e05cd1dddcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/services/websocket_service.dart", "hash": "1e361e377444cb52410f8b16b81128d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/lib/share_plus.dart", "hash": "b4073678e72f9fdcb1c38fc4733d5a78"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/LICENSE", "hash": "a27f596192906ba7cf9b6375cef4573a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_envelope.dart", "hash": "c951aee3d1c18f361fa079c7264f27ca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/breadcrumb_client.dart", "hash": "d294a319765b686f03846705130355c6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "hash": "96ed4c0b2ac486bba3db2c5d2a96afc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/http_sanitizer.dart", "hash": "21085c686b7a52650134181df6e91008"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/delegate.dart", "hash": "99eea31c698ade1eba0c70780f4c3c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_envelope_header.dart", "hash": "36b2951dadae0e73ee2d138974557bca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/enums.dart", "hash": "fcf700e37a2ca8372a19ea695ac704c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_bmp_string.dart", "hash": "dddc657850282e13df5f4944bd879f97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/dsn.dart", "hash": "de6bf4fd31534cda75a1afe9d911e877"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "dc025ebc977f56a895f49dc6d82a6d45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/types/types.dart", "hash": "102fd2fe72902d82633e70e32ec6ea7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_reader.dart", "hash": "7f909b315b723d7060fa20f099d03ba7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_bit_string.dart", "hash": "4504c6fed6df7d55aa56cf1aee1a1676"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/registry_factory_exception.dart", "hash": "a9f8eec89e7e84de670561aa40b57d66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/frame_io_helper.dart", "hash": "bd9ef30d8168e87f9e540db76f4426db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ierrorinfo.dart", "hash": "7ec176456b796d360121e28a6af41a2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/router.dart", "hash": "586f82935199530ba7ff15a9a7cbe00c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "873f842bb40bf6525129af58dab2e62d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemimagefactory.dart", "hash": "d04edc39b6d3477197606ec9c969e738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils.dart", "hash": "a50b339d8050df9fa1f095bb939c66a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp224r1.dart", "hash": "27d5ca1f907901597d5d2430f6a6ba31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqlite_api.dart", "hash": "5494fe877262550facf407b379edae59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/url_filter/url_filter_event_processor.dart", "hash": "ab278fed0e64deb7daf7919f462c9b14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_c.dart", "hash": "c849cfe9b5fed1c48d8eb868fbd5e4ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1.dart", "hash": "b6c036b7b0da523994ed5a60aaa7ea9e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iwbemhiperfenum.dart", "hash": "adebe1537e162fcbe4404ab29e94fef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/on_error_integration.dart", "hash": "5dd42f65041ec3c4beb57d9a791db57c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/frames_tracking/sentry_delayed_frames_tracker.dart", "hash": "38fe722de153e540161fc116d2946e8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "84de591d644b29f5e21052bd9c84263c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/error.dart", "hash": "a10eafbc71350955a16e4e787402780b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_utils.dart", "hash": "273bbaa2c38c7b25b6392d70dc93fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/span_data_convention.dart", "hash": "1613adbb57bd7e7627bffc871cd4301f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_auth_client.dart", "hash": "3bc24109049f63bedd0393f75bc23503"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/wlanapi.g.dart", "hash": "30191f66ed437888e9e12cdc67d37c95"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/widget_utils.dart", "hash": "4a8a4f8953c9c9c67d396efa599d8f27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_object_identifier_exception.dart", "hash": "68dba5a68c15abb96aa390afe73d789f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/exceptions.dart", "hash": "e3ef71f241dd9711d599fc18b59abdf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/ec_key_generator.dart", "hash": "306b0ac3fd1402ed468c5c00690214d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/xof.dart", "hash": "9868fd3987387a459685bafb3372ec41"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/widget_filter.dart", "hash": "7e8a03783dc3efb19c19f06f0ffd7fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/registry/registry.dart", "hash": "4b40445b5d011f54f19508bf46bc4ba8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "93fd05191baf9bfae1ae604f67d953b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/navigation/sentry_navigator_observer.dart", "hash": "a3a603172fed890a061f37e3db3c3057"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/srp_client.dart", "hash": "092fa998bb70518958eb737eda80dace"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/frames_tracking_integration.dart", "hash": "6fdca3deb79dac5324fe0adea8f5f403"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_generalized_time.dart", "hash": "78a64ae6ed0887ba9aac8e9bf202d0e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/task_queue.dart", "hash": "db33eb11792f2bbebdff6a0b58b01b55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "1fec236f729d3217c13d42295fe3faf5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/object_identifiers_database.dart", "hash": "c86737c1cc4387293239e818fee5be74"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/algorithm.dart", "hash": "97c871650da560b2968a14f9aa55c1a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinenacl-0.6.0/LICENSE", "hash": "0751fd899e052c9410838811726dc31f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifiledialog2.dart", "hash": "ef41b02d4257a466a4a68f493052b543"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/oaep.dart", "hash": "b20477f458f2d1ac22308240b4fabda8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/jvm/jvm_exception.dart", "hash": "e02bd989205924ce2534e9a55e5ff4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_mac.dart", "hash": "3adc21eeb3f32e17731c240849c1eb1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/firebase_core_exceptions.dart", "hash": "bc949707cfd60ff573b48a27b02f6756"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_gpu.dart", "hash": "fa0aa136f4bc81bc65b87bdafa2c9f91"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/exception_stacktrace_extractor.dart", "hash": "8af30d6b2f28a8a5f38660b7590b95b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/widgets_flutter_binding_integration.dart", "hash": "e335030b01ccaf32448b0c3ea58714ed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ac902f2f74549f89e0be0f739d94f7f6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/masking_config.dart", "hash": "f7d2177a5b83c37e2905c02dc10063b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/transport.dart", "hash": "95478c24cc93bc3fe1a16eeee30e23ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_boolean.dart", "hash": "922fed15740c84a21712faf7a96206d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pkcs12_parameter_generator.dart", "hash": "509c42eb7f8b047ecb2df2ad0c0973b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_error_name.dart", "hash": "7398500b1824f6043f23e208cd993866"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/default_extension_map.dart", "hash": "fe2df60ed5b05e922df2ee9fef5cf5d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/advapi32.g.dart", "hash": "e1c4eba9ccd9a12c58e4e531e73fcc32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha20poly1305.dart", "hash": "f470c01fea76057e483eb3148f90083c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/message.dart", "hash": "fe4f36f2c139e1900dbda797a7e07fc9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isequentialstream.dart", "hash": "2d06e55a087b389063f0d5777e1d8563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1utctime.dart", "hash": "06ddbe4bde64b07e4f14292fd52fc253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/schedule_mode.dart", "hash": "9979b67c6fdb803b55c4628af847ad4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/platform_interface/platform_interface_messaging.dart", "hash": "557cb1539058016704b06fde529507b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "hash": "2c21734ae994817f0963bcea30513c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/ole32.g.dart", "hash": "5be59a094b276fbbeb0a2255d1c45e2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_introspect.dart", "hash": "1d43aa18b7cd09879287a4e8ba5ea5ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_trace_origins.dart", "hash": "804ea76c9f866f407518c03b31ef93b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_id.dart", "hash": "2b69e2643904b49c3f7bb81294d29a1b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "hash": "d631809a6f4e20b7aa9ea7e17a6581de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileisinuse.dart", "hash": "9f2e86f55227535568e0459c4d51e139"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/types.dart", "hash": "98947dc53131e00bf0eb82564931fabf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ipersist.dart", "hash": "a1f73c43919636da8b8f9a657ca8cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_sequence.dart", "hash": "93ced0a5981ad9d669e7ff36f193f598"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/pss_signer.dart", "hash": "7b256fd525aca27690ce2f2621caf3bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_traces_sampling_decision.dart", "hash": "05e9cac0fb2d101407ad3da7d190449f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/method_channel_mappers.dart", "hash": "5d0fb3d359f4af000209c65b873ae97f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/failed_request_client.dart", "hash": "83fe13f09c8064871e356663ddccdaf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_engine.dart", "hash": "be8db0f0d8f9d7aef0bc2cb469f73907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha512.dart", "hash": "525412ef37eb7978b3b20aa955b9dfa2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/replay_quality.dart", "hash": "6db02cb60daa5c2562cff867c4f7945e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_feedback.dart", "hash": "d7239866581e275054ec6270daf0babb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeechobjecttokens.dart", "hash": "f87e5679793d9c81072018b428dadb8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/http_header_utils.dart", "hash": "024732fa5d91958856752ad7f1b59f07"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/flutter_local_notifications_linux.dart", "hash": "bd3131f212db4084582e634bc232b43b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/shell32.g.dart", "hash": "c1210af8f1663dc5959f1ec44acaa5a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/LICENSE", "hash": "57d76440fc5c9183c79d1747d18d2410"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/internet_connection_checker-1.0.0+1/LICENSE", "hash": "f497b3a4767b7cb3df0e27cd0535299c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd320.dart", "hash": "a7cad8a0ee91928a961ab734fa6ff3c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "fac91a50f448265e9a9f97994e8b529e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_view_hierarchy.dart", "hash": "2e2ffb0a216e55a7a8d90be2def0c8d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/integration.dart", "hash": "59155766f70d6a46a610372339413183"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/ios/enums.dart", "hash": "670388961b23da0ffd68cf26f5104e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/spotlight.dart", "hash": "1c5de4c7533585fb6fed3c95f2aab6fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160r1.dart", "hash": "802c6118f631a36eab3448fe69584b97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_attachment/sentry_attachment.dart", "hash": "521cbc1591165e2b3d62cdba6f2239d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/sentry_screenshot_widget.dart", "hash": "3a66f2caa9e4499cfd9291e99a15cb6b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_block_cipher.dart", "hash": "8ff5264ae4e3db190e31c45ad6f69cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "hash": "1c71712af9ddaeb93ab542740d6235fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/hive_aes_cipher.dart", "hash": "69a68782431189a163d7031587f20438"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key_parameter.dart", "hash": "e0a46b7ab15db018858fb7ed96b75785"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/file_version_info.dart", "hash": "6b943be06664ea45e0cac8c8178920b7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/firebase_options.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "71eaaef10eca13dd60c5459f65db0e72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_value.dart", "hash": "21beb4ff2c06d1edc806270e0bfac51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app.dart", "hash": "dec43cdc695f6ef4f0a33ae459c0e58c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/windows/file_picker_windows_ffi_types.dart", "hash": "61b775a84b287f0d0631b422b301ade0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iclassfactory.dart", "hash": "adbacdd68acdd5e35ce91a3475a1be38"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/types/auth_messages_windows.dart", "hash": "c88d29163ef68ff67276b64f3a1331cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/auto_seed_block_ctr_random.dart", "hash": "0e016aed1674f8e3cbc23abe63d5f108"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sm3.dart", "hash": "9191b515c6ecf451da97518f6307d935"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/big_text_style_information.dart", "hash": "e1c112a7342a7ee3110a1c2df175b89d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/misc/inherited_router.dart", "hash": "94325c70d85d9b1d588018f56c56adc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/flutter_exception_type_identifier.dart", "hash": "c191b3a772fdbc0fdc5be646782bbff6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_parameter.dart", "hash": "efffd75c3a5f665f679d9773bb0e4695"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp192r1.dart", "hash": "781f8d5b9335824b7f34534f48312377"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/tzdb.dart", "hash": "01c25b2dabe912c532a94956c2e40c8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationorcondition.dart", "hash": "821dcb1b139f1347a59141ff1fe42766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "d71b6121d7069ff8303334b41e9a92d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_isolate.dart", "hash": "11b96c68569c65baaf4ac84aab967066"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation2.dart", "hash": "c98cadb2fac8ead45ecaa10366da3ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "ebf62f8040320f913d52494eab3f3dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1bitstring.dart", "hash": "06b2ae65860a56ae73787c243966a98a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "8924d681363bacc7cd51c183b529f260"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "1f730a0a60de9aa207c05692d82791b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/hmac.dart", "hash": "68e9d53e365e08c6729d99c4f16a94de"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/regex_utils.dart", "hash": "075058435e254f21ab952c20c9ecaa34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/dbus_wrapper.dart", "hash": "52e0406df2babb2958beb4b471ccbcbe"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/c/binding.dart", "hash": "f4344998453a71be6d949edf956cd216"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/rsa.dart", "hash": "36da076a780a3a0d705082abb04835b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/propagation_context.dart", "hash": "43f0aac341c491dd1ad575d7101b53b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclient3.dart", "hash": "e65733ef6887a9505fca66a22d9e2887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/integrations.dart", "hash": "c0da2d47493bc3815c8516782ff41c68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/flutter_enricher_event_processor.dart", "hash": "c9ca8368afaab4ba5e922d8255670990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_list_impl.dart", "hash": "6f02ecb5b09b8edd2a435707a8516cef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_device.dart", "hash": "3bff379887ffec916f007773e5b72597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_app.dart", "hash": "0b984c17b2b3894974c7d249df145dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/dbus.dart", "hash": "59ba4a85ea18ab7b3030f370a0e93450"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/flutter_dotenv.dart", "hash": "abad4452b42cf93d5d9ff35493cda028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/scope.dart", "hash": "0532b3a423a4aaa3f0d009d358c19495"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "34f75dd4788c48a2a7b2ce2efe4c51fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_set.dart", "hash": "42fd90a5d728fea9edfaa04f4f1ee6d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/client_reports/discarded_event.dart", "hash": "9af67c38e233fa154c3462d378e5995e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/src/messages.g.dart", "hash": "3ff09a7edec90fdf07e59bc3514ea474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE", "hash": "3b83ef96387f14655fc854ddc3c6bd57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/constants_nodoc.dart", "hash": "7c9915d304f1ce53e7350d1c32ac98d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "3d2b72757d0604ae307bd71ceb16f6c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/connectivity_plus_linux.dart", "hash": "2aea038844961a04f31f81fbd8503cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/access_aware_map.dart", "hash": "984a2f92d3fef0c24e62a3c37f519793"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/structs.g.dart", "hash": "b248aab8f1807ae07bc22c26210f2def"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_runtime.dart", "hash": "880f9761e95708a3330e937379feb07b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/comctl32.g.dart", "hash": "f203522611d9d5ac9047af433e7f84f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/utils/platform_dispatcher_wrapper.dart", "hash": "37ae5730690c3a895f0cc5e2c5ef7087"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp512r1.dart", "hash": "760c33d66faa9169929f8c8d005222ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/notification_channel.dart", "hash": "bc48ae34e58774e84a72567a86034fef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_object_tree.dart", "hash": "eedac0b4fc9b2865aae62ba790f0e26a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/cbc.dart", "hash": "f99e630fbf8e81649ca291c95c896add"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/des_parameters.dart", "hash": "39880bf6cfebdca852998c5a638adfa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/secure_random.dart", "hash": "9e172299072e646efdc499bef0447fac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/blake2b.dart", "hash": "8571b58dd534486695b789e2aa75a7a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/origin.dart", "hash": "042760418165ed91352cfbe63d3873b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp384r1.dart", "hash": "88aa7805c269dbc20cf16073f7945e46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/replay/replay_config.dart", "hash": "a16964e0ccf09d1cdd85a0edd5aaed94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_tables.dart", "hash": "e086df7291d9d546cf582d0a519f9848"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispnotifysource.dart", "hash": "97fe81a282e612211e9648061d6d5dbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/platform_interface/platform_interface_firebase_plugin.dart", "hash": "07db573490cf88af2c2da7b393436779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_thread.dart", "hash": "1bca6d092aca3a8e677f2e8a6a57d857"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp320r1.dart", "hash": "383f9fc1ec6fea8eed35b8be5538e8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/initialization_settings.dart", "hash": "00883d18f109cb9b8f09707e277106c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/dxva2.g.dart", "hash": "9bbe69dd9a1b6e7cd87210c8fc19314e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/global_state.dart", "hash": "dc4e3bf96e9c6e94879d54eaa2f81c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/integrations/load_contexts_integration.dart", "hash": "de2eb547b8a9fea2e6a0c7be77d3ba52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "8944748ddfae167a4c9f3dc75a702e46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/client_reports/client_report.dart", "hash": "c4313121619d4ebb0924cb542fe020ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "21bea147ac9531ac715cd99a07f55866"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/widgets_binding_observer.dart", "hash": "91192f0fba02f9c570b8fa4f64e93abf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation6.dart", "hash": "a878c551495aae9f415d298f162fd19e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/LICENSE", "hash": "038c3f869f408e1194eda71cafcca6f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/flutter_local_notifications.dart", "hash": "6911901b1e6f800a6d433577dd9b93a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/LICENSE", "hash": "73f9b9d309f8eea6cde4289124f19268"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellservice.dart", "hash": "b92ed7d96a5284441953017edb47f285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp112r1.dart", "hash": "e2ef26fdbb565dd9481f925b51fc1bcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_generator_parameters.dart", "hash": "c7f97dafe25263ad7381f4b00c2ec24e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/continuation.dart", "hash": "33717fbf3f4de35b5e494d284a252bb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/screenshot_event_processor.dart", "hash": "7acf7ed6ae26576038db8624c44de2da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/sentry_flutter_options.dart", "hash": "84d72293ccf874e2f481915d501ce3a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/misc/errors.dart", "hash": "8cbd679f40c3f8e0bd00dbbd6bfb8f79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib/src/messages.g.dart", "hash": "2f0a0bc8c94bf7cd746c5fa5d47040ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/core/services/background_sync_service.dart", "hash": "7e102de1bae6ea39824cca3c9c936afd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworkconnection.dart", "hash": "21da671eb92823f3b4c91c47b2e9bac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha224.dart", "hash": "100657e7d45f57cd685d0e1a0d9a0143"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "e5349492be89ad5eea4187db08b2ad0f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitemarray.dart", "hash": "bd08457ce7d378f126bea891f669b69b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/sentry_mask_widget.dart", "hash": "9ca0a02d07b687cabd96a4ce9fc11431"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/nm.dart", "hash": "7494ac5a5e8b9d56894cd383fa6e9d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/dio.dart", "hash": "3059dceae50124dbd966f136c80016fe"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/win32_registry.dart", "hash": "78f302720a7202dc38e0922492f5c7c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudiocaptureclient.dart", "hash": "187bca624cdda52a572fde54e8395124"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/windows/file_picker_windows.dart", "hash": "8946ac68487ce85a69fe922d94fabc81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/user_interaction/sentry_user_interaction_widget.dart", "hash": "0654fbf99c8654c0af7883c6478cc663"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/immendpoint.dart", "hash": "ceac2a8f7197831de70d242e885a1527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_exception.dart", "hash": "2747964c64fe300f15d15123727cbcf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/src/helpers.dart", "hash": "dad9796d04d76633de091aec36be71c2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha3.dart", "hash": "14f0cde9213a6bba971bace260be637d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_culture.dart", "hash": "ea6122c0abc2bafe36fba636aa6a5c8e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/imetadataassemblyimport.dart", "hash": "dddc2f13e029b11ddffa36413341f1b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_printable_string.dart", "hash": "f021f69fef1a6d0b8a8870757b9023fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_write_buffer.dart", "hash": "63d2768cdd6ab5a282fbb6a86c237b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iinspectable.dart", "hash": "4a83689a30f6283c87f680b4c54bdd91"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_trace_header.dart", "hash": "f280d497399d14194ce2523a91a42d1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_tracer.dart", "hash": "86b9353e73d2e58f63a8af4cb87dcea3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getsid.dart", "hash": "5261078afe15bcdc637478bb6d7f7e21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader2.dart", "hash": "9e2940d007af19bd5cf177e3be339363"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/options.dart", "hash": "fd4b31aeef96e63881bfcd44031ae269"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_xcha.dart", "hash": "ebd5f3f78c5f54bee9f7bdf83de334ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/flutter_exception_event_processor.dart", "hash": "af215a3c7c3ceb3b86478b05b406cf21"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "hash": "41f10ec8505e0a41b39f72e7f9554119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/http_client/io_client_provider.dart", "hash": "e1d0a5e2859029702f96e0dc47f29ccf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1enumerated.dart", "hash": "15df5f6a6c0927c61ff5584926c53020"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensorcollection.dart", "hash": "c20dc5b81ea6dddfc61f66c603afd971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/utils.dart", "hash": "b3c645b67738d6348499b11034a6a801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/change_notifier.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomation3.dart", "hash": "64b70549a67d82ee25c435f5fc06ea49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "1dfa85bd16bf08ae91f9cceb02ef1563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/getuid_linux.dart", "hash": "cc4abe2eecf823ea14c55f9c5c09e203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/aes.dart", "hash": "2e1c42090ed6f3a2feddd00729eb467e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/encrypt.dart", "hash": "8b5fb284932f8980730cced7197be312"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/file_picker.dart", "hash": "ef26c7cdc66d3ab5c0cca5067d48c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/whirlpool.dart", "hash": "09dc184f9ccb3a851aa34deb56ecfd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/src/engine/transport/polling_transport.dart", "hash": "5e331345e2dc688cf2b07bbd1a50daf1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/native_scope_observer.dart", "hash": "b59eb7cc2ef68dfb7d1c26125dfc3ffc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestreader6.dart", "hash": "33186ffed4f0249b40a7d6161b7c2351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/development_filter.dart", "hash": "a925c024faf2d8bc047793e5a39b95d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/icon.dart", "hash": "cb4cf0d998a65879bb40daf8db093eed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/screenshot/recorder_config.dart", "hash": "c97df630ba4f222d8a8b12713a0adb52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iaudioclock.dart", "hash": "c31f7af379f7e5f653364d4a14a78750"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/box_extensions.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/feedback/sentry_feedback_widget.dart", "hash": "7a431a43eeda6d17f0fb9bb21ab5a9dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.5.0/LICENSE", "hash": "eec8df566dadd4c873d9e323a7e31bd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/linux_device_info.dart", "hash": "2989c1b0519c699587a5e31d87597fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/method_channel_helper.dart", "hash": "cc0346cee948c43689ab7c51a0e4939c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iknownfolder.dart", "hash": "561daa1b637bf14aa167a49b8fc3ce6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE", "hash": "fde2b1b7d744e3606529be50acb7fded"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha20.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/notification_details.dart", "hash": "fcb452549a7c86cdf118933be09ef427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/notifications_manager.dart", "hash": "ce45b60ad9b0d7c8690b9b1fae2b7f6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationelement2.dart", "hash": "4f061ba7ed2e408e218e0eb4375dddee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "5d461db74d04d7e270d13a5a8a340796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/LICENSE", "hash": "4076b6f7ff021f511a11282f13f0c61e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/native/java/sentry_native_java.dart", "hash": "41b83d69f32637b1482c93bef99a3c47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_b.dart", "hash": "1047083da015c385f105aba778621fa2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "hash": "1eadf5be3116dc31e5f04d4d6d352497"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapter.dart", "hash": "80079ed73f37411d422a28fb563580bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_registry.dart", "hash": "c17abfd46dd4cb9d6b286b913754f6fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp224r1.dart", "hash": "6e5c4bc7b03bcdfe3f17bc6d9c3efcd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/platform/platform.dart", "hash": "eaeb6077e2199ffe0cebe937dc3f5148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_method_call.dart", "hash": "da6f500c03c005a207d38c1daf24b00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/log.dart", "hash": "a7730cdfe094a3fdd076fcf5fe39ed65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/utils/iterable_utils.dart", "hash": "2eac9b662276d56bf1fd01d0e90e6f85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "1a04b09efdee92cd9f3e6c8f821820c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumspellingerror.dart", "hash": "4454497beed7948ccb9d6987d52ff3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_package.dart", "hash": "0d48bbf93f818ab8f035d5523e7be9df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/src/platform_interface/platform_interface_firebase.dart", "hash": "81402c8eea37df800d379c88bdcf6f44"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_read_buffer.dart", "hash": "fd517e61edeaf09f9e4cf9e9ba8af13c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime256v1.dart", "hash": "0057ceeb1027cb9f88a06733e0ea96f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumvariant.dart", "hash": "ad6fa6bf1dadc6e07c4c080c69abde6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/noop_transport.dart", "hash": "5fe7fc237cc2908a8d8d193eacacc3db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/event_processor/url_filter/io_url_filter_event_processor.dart", "hash": "83110fa6f853e6876a9bb72c8d986cb0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_digest.dart", "hash": "7eced463b747e7992e0e4a5ba21023ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/netapi32.g.dart", "hash": "242d63b96e4a26d3b557a32d0a008a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/parser.dart", "hash": "830859b7bec94f5f922eaba151827455"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/flutter_local_notifications.dart", "hash": "672695b311530b8c64badc8eb93e6fd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "9b52b890a7d94fe05f5f3ab8b7324b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/transform_empty_to_null.dart", "hash": "579bb0bd41c172690d80937bc1ce3b4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/hive.dart", "hash": "f038e71fe3279bb9c67e5ef28b3e8afe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_proxy.dart", "hash": "0ffd8e44c51ec0299168230030052b7c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/view_hierarchy/view_hierarchy_event_processor.dart", "hash": "06df2adfacd807f28215e1c0b83ce75b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute_io.dart", "hash": "e990b24e6368a3aa33f21b4695cfcfab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1constants.dart", "hash": "16dbb6f22fd8703424ee8f6d5338d911"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ienumresources.dart", "hash": "2e130b0e6cc669eb69235f142071943e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/char_code.dart", "hash": "4fb96b9e2073cadc554a25b36f55e6dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/extensions/filetime.dart", "hash": "562889498a1b0cda759a1186693143e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/throwable_mechanism.dart", "hash": "1b47e8544bdca691a01ef56c27bfa4c7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/cshake.dart", "hash": "26f7e7d2614abc62e37602284c38e8c6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/notification_sound.dart", "hash": "c0d5d7856094b4be15b738392704b921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/lib/src/notification_settings.dart", "hash": "c1da5c2e88951f9ab78eb684ea6ea005"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/frames_tracking/span_frame_metrics_collector.dart", "hash": "6fdb4ea938a16b34c8bf0083240670a2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/guid.dart", "hash": "831a91029162697310005b2ad492c0ae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/src/registry_value.dart", "hash": "3a76b28bc60cde234df7a417b1aa2ddb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_span_operations.dart", "hash": "0891d5fa4515dbd7bde9525bcf4a3967"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/constants.dart", "hash": "195aceb9dfe0dacbf39711b8622ce2b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/environment/keys.dart", "hash": "549be845b3d9bc53502555fdb16c4cec"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/lib/src/engine/parser/wtf8.dart", "hash": "fd6721cf17eaf105caa4e4af588cb8ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/impl.dart", "hash": "dc83669d5e8c3f6966706169ffe4c8a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/transport/data_category.dart", "hash": "bffb7f606c225064030dde2a0e52fc5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/lib/src/messaging.dart", "hash": "1ecaeac5cfcf8a553ea5107745304ba4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensordatareport.dart", "hash": "d241941a5420f01b81ee47fc755f8123"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp192t1.dart", "hash": "8d258031864a092670b4e4d486d371f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib/src/auth_messages_android.dart", "hash": "cd9db4ac4b35f0a15d74d1c6ce32a107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/date_time.dart", "hash": "7aa81f3512d4aac3df17ea133452bbcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/navigation/time_to_full_display_tracker.dart", "hash": "31b567bb007d1142df45e8fdde9263b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_teletext_string.dart", "hash": "8fb1cb74a8c527ab6dbb3d5ee8a44ab8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispeventsource.dart", "hash": "33ce76d75b24f6c7ed6ad95a422b76b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_span_context.dart", "hash": "ef4e1095bc2fd422bc593da973cee40e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/lib/src/logging.dart", "hash": "5872689884d3985685f0239a1f89f71f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "509de531546dd357cb81de8c9e42312d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/interruption_level.dart", "hash": "3667c2cba3e0537e66b40353a1482487"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ec_standard_curve_constructor.dart", "hash": "f4c21a708c21cecc2823a65315ab890b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "8b65a0312de1594ea0989e8ce1d4b257"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_mixin.dart", "hash": "e103c51878b3741ffe4d81896876f3ef"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/features/wallet/providers/wallet_provider.dart", "hash": "306196b483daf3c7c1d997b6c4b7a5b5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/LICENSE", "hash": "0d2e75837433e7f55da5ce1145a85597"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_iv.dart", "hash": "dcac631b5103a400096235ac0d1b8a85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/watch_box_builder.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/src/model/location.dart", "hash": "17db713e9a12494613ca23ad84def9c3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser.dart", "hash": "b8a405a7e5ea8001bb0ab36de015ac6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_geo.dart", "hash": "58bac0e33bfc7097ed0af1155ae55a2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/parser.dart", "hash": "505fb0828e4fe58e1a49ddab272d7b71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/local_auth.dart", "hash": "32e01ce1b6c445348bbeb73c4610288b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/model/base_device_info.dart", "hash": "4f0e33807b3ea8a3a5d03a85dbdf565f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/progress_stream/io_progress_stream.dart", "hash": "6ea89c3bc6b0860bd7c16998d3950c3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/features/chat/providers/chat_provider.dart", "hash": "3b8dd7d36cdc3ab05634bbad8f3e61bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/frame_callback_handler.dart", "hash": "794c01c6a48530fb227a18fc9d1d3ef0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestapplication.dart", "hash": "bc01545a1cca050f2067c0b6163a4755"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1sequence.dart", "hash": "891b2d9b6bf04296e789fb20012e5908"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box_collection/box_collection.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/invalid_sentry_trace_header_exception.dart", "hash": "afed6e9a5cfa209642f23f8721995bb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "30bad556275cf4f7a39d50f698375871"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/lib/socket_io_client.dart", "hash": "344b9239099198a69e90bf6b4a202f9e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_cbc_pkcs7.dart", "hash": "93042b4972c8255fa75112f440f77aea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/exception_cause.dart", "hash": "da839d6fb729dd03dcc4d84a31e0eaf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/darwin/notification_action_option.dart", "hash": "be2d4c688f4ca84e68eefd04fe0ed129"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "cb223d2445f2caf7a2617e25ca761ff4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/kernel32.g.dart", "hash": "6bb547ebfa35dd1c7acaa81eedf39905"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp384t1.dart", "hash": "c1912c537c302fffda915c4c03584ce0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_exception_factory.dart", "hash": "73ad942078076e4ccd8fb6c1626a0dcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response.dart", "hash": "2a02594ad813d39d23460e2abfd2551d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/navigation/sentry_display_widget.dart", "hash": "3b5f3cb122b4029606a3e977eb22f99a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/contexts.dart", "hash": "448ef0aef0eb0dd1ee51e32d19fec5da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/version.dart", "hash": "902b1c85ce4d38d380b7821e8f5faa01"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp320t1.dart", "hash": "4f64dcf9751c389b5608c9247ae89a91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/plural_rules.dart", "hash": "2241f880365723564463d0bec35a4ba2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "8355566a31f02cb53e7f9b94d8c873ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/src/env.dart", "hash": "278242320426f869a4121f48b98c2ed9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/enums.dart", "hash": "a4b97395630dc415cc76f514d4a38869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/b01bf0d1026b477a0a2df2842c7e9f2d/app.dill", "hash": "1f730a0a60de9aa207c05692d82791b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ifileopendialog.dart", "hash": "54b556c56a02a636de1790f953f298bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "05fbdfb1bb8ed12098aa521c31a145ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha512t.dart", "hash": "9cbd64b569333e314236ab20ed1f6ef6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/src/platform_specifics/android/styles/inbox_style_information.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_tracer_finish_status.dart", "hash": "190bfec843d3a76743947b27372c7174"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "0e619c36f088b986b65eadb12698abb8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/lib/shared/models/user_model.dart", "hash": "205e44503af21cb6f4c86d26749b7e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_attribute_type_and_value.dart", "hash": "bfcbc06b75397407cc5bc0c3c9775c84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_safe_bag.dart", "hash": "da5ae7edadbd48f3bf8538f8649249b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/origin_io.dart", "hash": "cb848712bd13c11293ebd6b313d6124d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isimpleaudiovolume.dart", "hash": "a064bc8b49ee4e47fd7b996364a8469e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/protocol/sentry_stack_trace.dart", "hash": "344b6e7294450b937a9e288a6b2784f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padded_block_cipher.dart", "hash": "54199c9259c98b0a291f90a10e4a89cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/advanced_file_output.dart", "hash": "b485ef67fbe801f44114a023c4458b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/file_selector_macos.dart", "hash": "20f3c0d39cbc5c2fdb223745edcecdec"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/cmac.dart", "hash": "ee4ce4a2c979a2ddf7bc31f42ba327dc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/asn1lib.dart", "hash": "5d418a9e868070cb636b7b95a3c0be7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/isensor.dart", "hash": "9d1c0eb5292b2112e1b43affe9a2cadc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.8.7/LICENSE", "hash": "1ac261c28033869c8bcf9caaedf74f6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/lib/src/profiling.dart", "hash": "b11981bbc94da8c4c6c6758bfa14ede1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/date_format_internal.dart", "hash": "46f06f2d32f61a3ebc7393f1ae97df27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/sentry_event_like.dart", "hash": "a8de373e361c54bfe447b16f67f6aa73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/src/asn1object.dart", "hash": "2454dd0050e99b71559538bb01e04bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/lib/src/file_picker_macos.dart", "hash": "6c20c9f1411de82078eddf91bb2a343c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE", "hash": "80ae6870ab712d32cc9dff7f6174b603"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/winmd_constants.dart", "hash": "16115596ace5bc18b10c61743655c625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "hash": "d0268b4d80612385359eadd2d6ddb257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/src/dbus_interface_name.dart", "hash": "4f835012742ef22df8c85292594f9823"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/lib/src/model/macos_device_info.dart", "hash": "2dad016b21ffd8671999ec7fee53d20c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ecb.dart", "hash": "ea046544fb5b729368584b6b1b2888a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/lib/src/scope_observer.dart", "hash": "f21767c2cd27286e3e72111221227ae4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/src/com/ishellitem.dart", "hash": "6e25bd87f1ef3a06c65b27f722fff88b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_asn1_tag_exception.dart", "hash": "b699077c09fbd1eef398dd92fe7f2d03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eip1559-0.6.2/LICENSE", "hash": "47e988f714d6aecff66b473ef961d70c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}]}