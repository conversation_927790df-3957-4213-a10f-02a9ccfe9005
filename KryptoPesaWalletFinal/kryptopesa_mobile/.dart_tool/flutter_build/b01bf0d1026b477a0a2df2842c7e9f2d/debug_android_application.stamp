{"inputs": ["/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/b01bf0d1026b477a0a2df2842c7e9f2d/app.dill", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/pubspec.yaml", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.env", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/fonts/materialdesignicons-webfont.ttf", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/.dart_tool/flutter_build/b01bf0d1026b477a0a2df2842c7e9f2d/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/bip39-1.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.14/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_hd_key-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/eip1559-0.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/eip55-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.17.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.37/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.8.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hex-0.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/internet_connection_checker-1.0.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_rpc_2-3.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-5.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pinenacl-0.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sec-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry-8.14.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-8.14.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-9.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-2.0.3+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/wallet-0.0.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web3dart-2.7.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/LICENSE", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/DOES_NOT_EXIST_RERUN_FOR_WILDCARD835858519"], "outputs": ["/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/.env", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/packages/material_design_icons_flutter/lib/fonts/materialdesignicons-webfont.ttf", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]}