  
permission android.Manifest  CAMERA android.Manifest.permission  Activity android.app  applicationContext android.app.Activity  display android.app.Activity  Context android.content  DISPLAY_SERVICE android.content.Context  WINDOW_SERVICE android.content.Context  getSystemService android.content.Context  applicationContext android.content.ContextWrapper  display android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  ImageFormat android.graphics  Matrix android.graphics  Point android.graphics  Rect android.graphics  SurfaceTexture android.graphics  YuvImage android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  PNG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  NV21 android.graphics.ImageFormat  YUV_420_888 android.graphics.ImageFormat  
postRotate android.graphics.Matrix  mapOf android.graphics.Point  to android.graphics.Point  x android.graphics.Point  y android.graphics.Point  bottom android.graphics.Rect  contains android.graphics.Rect  emptyMap android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  mapOf android.graphics.Rect  right android.graphics.Rect  size android.graphics.Rect  to android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  setDefaultBufferSize android.graphics.SurfaceTexture  compressToJpeg android.graphics.YuvImage  height android.graphics.YuvImage  width android.graphics.YuvImage  DisplayManager android.hardware.display  DisplayListener 'android.hardware.display.DisplayManager  registerDisplayListener 'android.hardware.display.DisplayManager  unregisterDisplayListener 'android.hardware.display.DisplayManager  Image 
android.media  	ByteArray android.media.Image  ByteArrayOutputStream android.media.Image  ImageFormat android.media.Image  Plane android.media.Image  Rect android.media.Image  YuvImage android.media.Image  height android.media.Image  planes android.media.Image  width android.media.Image  buffer android.media.Image.Plane  pixelStride android.media.Image.Plane  	rowStride android.media.Image.Plane  Uri android.net  fromFile android.net.Uri  Build 
android.os  Handler 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  
Allocation android.renderscript  Element android.renderscript  RenderScript android.renderscript  ScriptIntrinsicYuvToRGB android.renderscript  Type android.renderscript  USAGE_SCRIPT android.renderscript.Allocation  copyFrom android.renderscript.Allocation  copyTo android.renderscript.Allocation  createTyped android.renderscript.Allocation  type android.renderscript.Allocation  	RGBA_8888 android.renderscript.Element  U8 android.renderscript.Element  U8_4 android.renderscript.Element  create !android.renderscript.RenderScript  forEach android.renderscript.Script  create ,android.renderscript.ScriptIntrinsicYuvToRGB  forEach ,android.renderscript.ScriptIntrinsicYuvToRGB  setInput ,android.renderscript.ScriptIntrinsicYuvToRGB  Builder android.renderscript.Type  x android.renderscript.Type  y android.renderscript.Type  yuv android.renderscript.Type  create !android.renderscript.Type.Builder  setX !android.renderscript.Type.Builder  setY !android.renderscript.Type.Builder  setYuvFormat !android.renderscript.Type.Builder  Size android.util  height android.util.Size  width android.util.Size  Display android.view  Surface android.view  
WindowManager android.view  rotation android.view.Display  
ROTATION_0 android.view.Surface  ROTATION_180 android.view.Surface  defaultDisplay android.view.WindowManager  IntDef androidx.annotation  RequiresApi androidx.annotation  VisibleForTesting androidx.annotation  Camera androidx.camera.core  
CameraControl androidx.camera.core  
CameraInfo androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ImageAnalysis androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  ResolutionInfo androidx.camera.core  SurfaceRequest androidx.camera.core  
TorchState androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  let androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  
setLinearZoom "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  cameraState androidx.camera.core.CameraInfo  hasFlashUnit androidx.camera.core.CameraInfo  let androidx.camera.core.CameraInfo  sensorRotationDegrees androidx.camera.core.CameraInfo  
torchState androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  apply "androidx.camera.core.ImageAnalysis  
captureOutput "androidx.camera.core.ImageAnalysis  resolutionInfo "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setResolutionSelector *androidx.camera.core.ImageAnalysis.Builder  setTargetResolution *androidx.camera.core.ImageAnalysis.Builder  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  height androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  width androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  apply androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  
resolution #androidx.camera.core.ResolutionInfo  provideSurface #androidx.camera.core.SurfaceRequest  
resolution #androidx.camera.core.SurfaceRequest  OFF androidx.camera.core.TorchState  ON androidx.camera.core.TorchState  
linearZoom androidx.camera.core.ZoomState  ResolutionSelector 'androidx.camera.core.resolutionselector  ResolutionStrategy 'androidx.camera.core.resolutionselector  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  'FALLBACK_RULE_CLOSEST_HIGHER_THEN_LOWER :androidx.camera.core.resolutionselector.ResolutionStrategy  ProcessCameraProvider androidx.camera.lifecycle  availableCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  Consumer androidx.core.util  <SAM-CONSTRUCTOR> androidx.core.util.Consumer  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  Observer androidx.lifecycle  observe androidx.lifecycle.LiveData  removeObservers androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  close .com.google.mlkit.vision.barcode.BarcodeScanner  let .com.google.mlkit.vision.barcode.BarcodeScanner  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  Address .com.google.mlkit.vision.barcode.common.Barcode  CalendarDateTime .com.google.mlkit.vision.barcode.common.Barcode  
CalendarEvent .com.google.mlkit.vision.barcode.common.Barcode  ContactInfo .com.google.mlkit.vision.barcode.common.Barcode  
DriverLicense .com.google.mlkit.vision.barcode.common.Barcode  Email .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_ALL_FORMATS .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_AZTEC .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODABAR .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_128 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_39 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_93 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_DATA_MATRIX .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_EAN_13 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_EAN_8 .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_ITF .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_PDF417 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UNKNOWN .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UPC_A .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UPC_E .com.google.mlkit.vision.barcode.common.Barcode  GeoPoint .com.google.mlkit.vision.barcode.common.Barcode  
PersonName .com.google.mlkit.vision.barcode.common.Barcode  Phone .com.google.mlkit.vision.barcode.common.Barcode  Sms .com.google.mlkit.vision.barcode.common.Barcode  UrlBookmark .com.google.mlkit.vision.barcode.common.Barcode  WiFi .com.google.mlkit.vision.barcode.common.Barcode  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  
calendarEvent .com.google.mlkit.vision.barcode.common.Barcode  contactInfo .com.google.mlkit.vision.barcode.common.Barcode  cornerPoints .com.google.mlkit.vision.barcode.common.Barcode  data .com.google.mlkit.vision.barcode.common.Barcode  displayValue .com.google.mlkit.vision.barcode.common.Barcode  
driverLicense .com.google.mlkit.vision.barcode.common.Barcode  email .com.google.mlkit.vision.barcode.common.Barcode  format .com.google.mlkit.vision.barcode.common.Barcode  geoPoint .com.google.mlkit.vision.barcode.common.Barcode  map .com.google.mlkit.vision.barcode.common.Barcode  mapOf .com.google.mlkit.vision.barcode.common.Barcode  phone .com.google.mlkit.vision.barcode.common.Barcode  rawBytes .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  size .com.google.mlkit.vision.barcode.common.Barcode  sms .com.google.mlkit.vision.barcode.common.Barcode  to .com.google.mlkit.vision.barcode.common.Barcode  url .com.google.mlkit.vision.barcode.common.Barcode  	valueType .com.google.mlkit.vision.barcode.common.Barcode  wifi .com.google.mlkit.vision.barcode.common.Barcode  addressLines 6com.google.mlkit.vision.barcode.common.Barcode.Address  map 6com.google.mlkit.vision.barcode.common.Barcode.Address  mapOf 6com.google.mlkit.vision.barcode.common.Barcode.Address  to 6com.google.mlkit.vision.barcode.common.Barcode.Address  type 6com.google.mlkit.vision.barcode.common.Barcode.Address  rawValue ?com.google.mlkit.vision.barcode.common.Barcode.CalendarDateTime  data <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  description <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  end <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  location <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  mapOf <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	organizer <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  start <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  status <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  summary <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  to <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	addresses :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  data :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  emails :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  map :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  mapOf :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  name :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  organization :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  phones :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  title :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  to :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  urls :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  addressCity <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  addressState <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
addressStreet <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
addressZip <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	birthDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  data <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  documentType <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
expiryDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	firstName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  gender <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	issueDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  issuingCountry <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  lastName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
licenseNumber <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  mapOf <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
middleName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  to <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  address 4com.google.mlkit.vision.barcode.common.Barcode.Email  body 4com.google.mlkit.vision.barcode.common.Barcode.Email  data 4com.google.mlkit.vision.barcode.common.Barcode.Email  mapOf 4com.google.mlkit.vision.barcode.common.Barcode.Email  subject 4com.google.mlkit.vision.barcode.common.Barcode.Email  to 4com.google.mlkit.vision.barcode.common.Barcode.Email  type 4com.google.mlkit.vision.barcode.common.Barcode.Email  data 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  lat 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  lng 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  mapOf 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  to 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  data 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  first 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  
formattedName 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  last 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  mapOf 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  middle 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  prefix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  
pronunciation 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  suffix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  to 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  data 4com.google.mlkit.vision.barcode.common.Barcode.Phone  mapOf 4com.google.mlkit.vision.barcode.common.Barcode.Phone  number 4com.google.mlkit.vision.barcode.common.Barcode.Phone  to 4com.google.mlkit.vision.barcode.common.Barcode.Phone  type 4com.google.mlkit.vision.barcode.common.Barcode.Phone  data 2com.google.mlkit.vision.barcode.common.Barcode.Sms  mapOf 2com.google.mlkit.vision.barcode.common.Barcode.Sms  message 2com.google.mlkit.vision.barcode.common.Barcode.Sms  phoneNumber 2com.google.mlkit.vision.barcode.common.Barcode.Sms  to 2com.google.mlkit.vision.barcode.common.Barcode.Sms  data :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  mapOf :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  title :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  to :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  url :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  data 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  encryptionType 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  mapOf 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  password 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  ssid 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  to 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  
InputImage com.google.mlkit.vision.common  fromFilePath )com.google.mlkit.vision.common.InputImage  fromMediaImage )com.google.mlkit.vision.common.InputImage  Activity dev.steenbakker.mobile_scanner  
ActivityAware dev.steenbakker.mobile_scanner  ActivityCompat dev.steenbakker.mobile_scanner  ActivityPluginBinding dev.steenbakker.mobile_scanner  AlreadyStarted dev.steenbakker.mobile_scanner  AlreadyStopped dev.steenbakker.mobile_scanner  AnalyzerErrorCallback dev.steenbakker.mobile_scanner  AnalyzerSuccessCallback dev.steenbakker.mobile_scanner  Any dev.steenbakker.mobile_scanner  Array dev.steenbakker.mobile_scanner  Barcode dev.steenbakker.mobile_scanner  BarcodeFormats dev.steenbakker.mobile_scanner  BarcodeHandler dev.steenbakker.mobile_scanner  BarcodeScanner dev.steenbakker.mobile_scanner  BarcodeScannerOptions dev.steenbakker.mobile_scanner  BarcodeScanning dev.steenbakker.mobile_scanner  BinaryMessenger dev.steenbakker.mobile_scanner  Bitmap dev.steenbakker.mobile_scanner  Boolean dev.steenbakker.mobile_scanner  Build dev.steenbakker.mobile_scanner  	ByteArray dev.steenbakker.mobile_scanner  ByteArrayOutputStream dev.steenbakker.mobile_scanner  "CAMERA_PERMISSIONS_REQUEST_ONGOING dev.steenbakker.mobile_scanner  *CAMERA_PERMISSIONS_REQUEST_ONGOING_MESSAGE dev.steenbakker.mobile_scanner  Camera dev.steenbakker.mobile_scanner  CameraError dev.steenbakker.mobile_scanner  CameraSelector dev.steenbakker.mobile_scanner  Context dev.steenbakker.mobile_scanner  
ContextCompat dev.steenbakker.mobile_scanner  DetectionSpeed dev.steenbakker.mobile_scanner  DisplayManager dev.steenbakker.mobile_scanner  Double dev.steenbakker.mobile_scanner  EventChannel dev.steenbakker.mobile_scanner  	Exception dev.steenbakker.mobile_scanner  ExperimentalGetImage dev.steenbakker.mobile_scanner  File dev.steenbakker.mobile_scanner  Float dev.steenbakker.mobile_scanner  
FlutterPlugin dev.steenbakker.mobile_scanner  Handler dev.steenbakker.mobile_scanner  IllegalArgumentException dev.steenbakker.mobile_scanner  Image dev.steenbakker.mobile_scanner  
ImageAnalysis dev.steenbakker.mobile_scanner  ImageFormat dev.steenbakker.mobile_scanner  
ImageProxy dev.steenbakker.mobile_scanner  
InputImage dev.steenbakker.mobile_scanner  Int dev.steenbakker.mobile_scanner  IntArray dev.steenbakker.mobile_scanner  LifecycleOwner dev.steenbakker.mobile_scanner  List dev.steenbakker.mobile_scanner  Long dev.steenbakker.mobile_scanner  Looper dev.steenbakker.mobile_scanner  Map dev.steenbakker.mobile_scanner  Matrix dev.steenbakker.mobile_scanner  
MethodCall dev.steenbakker.mobile_scanner  
MethodChannel dev.steenbakker.mobile_scanner  
MobileScanner dev.steenbakker.mobile_scanner  MobileScannerCallback dev.steenbakker.mobile_scanner  MobileScannerErrorCallback dev.steenbakker.mobile_scanner  MobileScannerHandler dev.steenbakker.mobile_scanner  MobileScannerPermissions dev.steenbakker.mobile_scanner   MobileScannerPermissionsListener dev.steenbakker.mobile_scanner  MobileScannerPlugin dev.steenbakker.mobile_scanner  MobileScannerStartParameters dev.steenbakker.mobile_scanner  MobileScannerStartedCallback dev.steenbakker.mobile_scanner  MutableList dev.steenbakker.mobile_scanner  NoCamera dev.steenbakker.mobile_scanner  PackageManager dev.steenbakker.mobile_scanner  PluginRegistry dev.steenbakker.mobile_scanner  Point dev.steenbakker.mobile_scanner  Preview dev.steenbakker.mobile_scanner  ProcessCameraProvider dev.steenbakker.mobile_scanner  REQUEST_CODE dev.steenbakker.mobile_scanner  Rect dev.steenbakker.mobile_scanner   RequestPermissionsResultListener dev.steenbakker.mobile_scanner  ResolutionSelector dev.steenbakker.mobile_scanner  ResolutionStrategy dev.steenbakker.mobile_scanner  ResultCallback dev.steenbakker.mobile_scanner  Size dev.steenbakker.mobile_scanner  String dev.steenbakker.mobile_scanner  Suppress dev.steenbakker.mobile_scanner  Surface dev.steenbakker.mobile_scanner  TextureRegistry dev.steenbakker.mobile_scanner  
TorchState dev.steenbakker.mobile_scanner  TorchStateCallback dev.steenbakker.mobile_scanner  Unit dev.steenbakker.mobile_scanner  Uri dev.steenbakker.mobile_scanner  VisibleForTesting dev.steenbakker.mobile_scanner  
WindowManager dev.steenbakker.mobile_scanner  YuvImage dev.steenbakker.mobile_scanner  YuvToRgbConverter dev.steenbakker.mobile_scanner  ZoomNotInRange dev.steenbakker.mobile_scanner  ZoomScaleStateCallback dev.steenbakker.mobile_scanner  ZoomWhenStopped dev.steenbakker.mobile_scanner  apply dev.steenbakker.mobile_scanner  arrayOf dev.steenbakker.mobile_scanner  
captureOutput dev.steenbakker.mobile_scanner  data dev.steenbakker.mobile_scanner  emptyMap dev.steenbakker.mobile_scanner  first dev.steenbakker.mobile_scanner  fromRawValue dev.steenbakker.mobile_scanner  
getResolution dev.steenbakker.mobile_scanner  isEmpty dev.steenbakker.mobile_scanner  
isNotEmpty dev.steenbakker.mobile_scanner  let dev.steenbakker.mobile_scanner  listener dev.steenbakker.mobile_scanner  map dev.steenbakker.mobile_scanner  
mapNotNull dev.steenbakker.mobile_scanner  mapOf dev.steenbakker.mobile_scanner  
mutableListOf dev.steenbakker.mobile_scanner  ongoing dev.steenbakker.mobile_scanner  
permission dev.steenbakker.mobile_scanner  
roundToInt dev.steenbakker.mobile_scanner  size dev.steenbakker.mobile_scanner  sorted dev.steenbakker.mobile_scanner  to dev.steenbakker.mobile_scanner  toByteArray dev.steenbakker.mobile_scanner  
toIntArray dev.steenbakker.mobile_scanner  Address &dev.steenbakker.mobile_scanner.Barcode  
CalendarEvent &dev.steenbakker.mobile_scanner.Barcode  ContactInfo &dev.steenbakker.mobile_scanner.Barcode  
DriverLicense &dev.steenbakker.mobile_scanner.Barcode  Email &dev.steenbakker.mobile_scanner.Barcode  GeoPoint &dev.steenbakker.mobile_scanner.Barcode  
PersonName &dev.steenbakker.mobile_scanner.Barcode  Phone &dev.steenbakker.mobile_scanner.Barcode  Sms &dev.steenbakker.mobile_scanner.Barcode  UrlBookmark &dev.steenbakker.mobile_scanner.Barcode  WiFi &dev.steenbakker.mobile_scanner.Barcode  EventChannel -dev.steenbakker.mobile_scanner.BarcodeHandler  Handler -dev.steenbakker.mobile_scanner.BarcodeHandler  Looper -dev.steenbakker.mobile_scanner.BarcodeHandler  eventChannel -dev.steenbakker.mobile_scanner.BarcodeHandler  	eventSink -dev.steenbakker.mobile_scanner.BarcodeHandler  publishEvent -dev.steenbakker.mobile_scanner.BarcodeHandler  DisplayListener -dev.steenbakker.mobile_scanner.DisplayManager  	EventSink +dev.steenbakker.mobile_scanner.EventChannel  
StreamHandler +dev.steenbakker.mobile_scanner.EventChannel  FlutterPluginBinding ,dev.steenbakker.mobile_scanner.FlutterPlugin  MethodCallHandler ,dev.steenbakker.mobile_scanner.MethodChannel  Result ,dev.steenbakker.mobile_scanner.MethodChannel  Activity ,dev.steenbakker.mobile_scanner.MobileScanner  AlreadyStarted ,dev.steenbakker.mobile_scanner.MobileScanner  AlreadyStopped ,dev.steenbakker.mobile_scanner.MobileScanner  AnalyzerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  AnalyzerSuccessCallback ,dev.steenbakker.mobile_scanner.MobileScanner  Any ,dev.steenbakker.mobile_scanner.MobileScanner  Barcode ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScanner ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScannerOptions ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScanning ,dev.steenbakker.mobile_scanner.MobileScanner  Bitmap ,dev.steenbakker.mobile_scanner.MobileScanner  Boolean ,dev.steenbakker.mobile_scanner.MobileScanner  Build ,dev.steenbakker.mobile_scanner.MobileScanner  ByteArrayOutputStream ,dev.steenbakker.mobile_scanner.MobileScanner  Camera ,dev.steenbakker.mobile_scanner.MobileScanner  CameraError ,dev.steenbakker.mobile_scanner.MobileScanner  CameraSelector ,dev.steenbakker.mobile_scanner.MobileScanner  Context ,dev.steenbakker.mobile_scanner.MobileScanner  
ContextCompat ,dev.steenbakker.mobile_scanner.MobileScanner  DetectionSpeed ,dev.steenbakker.mobile_scanner.MobileScanner  DisplayManager ,dev.steenbakker.mobile_scanner.MobileScanner  Double ,dev.steenbakker.mobile_scanner.MobileScanner  	Exception ,dev.steenbakker.mobile_scanner.MobileScanner  ExperimentalGetImage ,dev.steenbakker.mobile_scanner.MobileScanner  Float ,dev.steenbakker.mobile_scanner.MobileScanner  Handler ,dev.steenbakker.mobile_scanner.MobileScanner  IllegalArgumentException ,dev.steenbakker.mobile_scanner.MobileScanner  
ImageAnalysis ,dev.steenbakker.mobile_scanner.MobileScanner  
ImageProxy ,dev.steenbakker.mobile_scanner.MobileScanner  
InputImage ,dev.steenbakker.mobile_scanner.MobileScanner  Int ,dev.steenbakker.mobile_scanner.MobileScanner  LifecycleOwner ,dev.steenbakker.mobile_scanner.MobileScanner  List ,dev.steenbakker.mobile_scanner.MobileScanner  Long ,dev.steenbakker.mobile_scanner.MobileScanner  Looper ,dev.steenbakker.mobile_scanner.MobileScanner  Map ,dev.steenbakker.mobile_scanner.MobileScanner  Matrix ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerStartParameters ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerStartedCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MutableList ,dev.steenbakker.mobile_scanner.MobileScanner  NoCamera ,dev.steenbakker.mobile_scanner.MobileScanner  Preview ,dev.steenbakker.mobile_scanner.MobileScanner  ProcessCameraProvider ,dev.steenbakker.mobile_scanner.MobileScanner  Rect ,dev.steenbakker.mobile_scanner.MobileScanner  ResolutionSelector ,dev.steenbakker.mobile_scanner.MobileScanner  ResolutionStrategy ,dev.steenbakker.mobile_scanner.MobileScanner  Size ,dev.steenbakker.mobile_scanner.MobileScanner  String ,dev.steenbakker.mobile_scanner.MobileScanner  Suppress ,dev.steenbakker.mobile_scanner.MobileScanner  Surface ,dev.steenbakker.mobile_scanner.MobileScanner  TextureRegistry ,dev.steenbakker.mobile_scanner.MobileScanner  
TorchState ,dev.steenbakker.mobile_scanner.MobileScanner  TorchStateCallback ,dev.steenbakker.mobile_scanner.MobileScanner  Unit ,dev.steenbakker.mobile_scanner.MobileScanner  Uri ,dev.steenbakker.mobile_scanner.MobileScanner  VisibleForTesting ,dev.steenbakker.mobile_scanner.MobileScanner  
WindowManager ,dev.steenbakker.mobile_scanner.MobileScanner  YuvToRgbConverter ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomNotInRange ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomScaleStateCallback ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomWhenStopped ,dev.steenbakker.mobile_scanner.MobileScanner  activity ,dev.steenbakker.mobile_scanner.MobileScanner  analyzeImage ,dev.steenbakker.mobile_scanner.MobileScanner  apply ,dev.steenbakker.mobile_scanner.MobileScanner  barcodeScannerFactory ,dev.steenbakker.mobile_scanner.MobileScanner  camera ,dev.steenbakker.mobile_scanner.MobileScanner  cameraProvider ,dev.steenbakker.mobile_scanner.MobileScanner  
captureOutput ,dev.steenbakker.mobile_scanner.MobileScanner  data ,dev.steenbakker.mobile_scanner.MobileScanner  defaultBarcodeScannerFactory ,dev.steenbakker.mobile_scanner.MobileScanner  detectionSpeed ,dev.steenbakker.mobile_scanner.MobileScanner  detectionTimeout ,dev.steenbakker.mobile_scanner.MobileScanner  displayListener ,dev.steenbakker.mobile_scanner.MobileScanner  dispose ,dev.steenbakker.mobile_scanner.MobileScanner  
getResolution ,dev.steenbakker.mobile_scanner.MobileScanner  isBarcodeInScanWindow ,dev.steenbakker.mobile_scanner.MobileScanner  
isNotEmpty ,dev.steenbakker.mobile_scanner.MobileScanner  	isStopped ,dev.steenbakker.mobile_scanner.MobileScanner  lastScanned ,dev.steenbakker.mobile_scanner.MobileScanner  let ,dev.steenbakker.mobile_scanner.MobileScanner  map ,dev.steenbakker.mobile_scanner.MobileScanner  
mapNotNull ,dev.steenbakker.mobile_scanner.MobileScanner  mobileScannerCallback ,dev.steenbakker.mobile_scanner.MobileScanner  mobileScannerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  
mutableListOf ,dev.steenbakker.mobile_scanner.MobileScanner  preview ,dev.steenbakker.mobile_scanner.MobileScanner  
resetScale ,dev.steenbakker.mobile_scanner.MobileScanner  returnImage ,dev.steenbakker.mobile_scanner.MobileScanner  rotateBitmap ,dev.steenbakker.mobile_scanner.MobileScanner  
roundToInt ,dev.steenbakker.mobile_scanner.MobileScanner  
scanWindow ,dev.steenbakker.mobile_scanner.MobileScanner  scanner ,dev.steenbakker.mobile_scanner.MobileScanner  scannerTimeout ,dev.steenbakker.mobile_scanner.MobileScanner  setScale ,dev.steenbakker.mobile_scanner.MobileScanner  sorted ,dev.steenbakker.mobile_scanner.MobileScanner  start ,dev.steenbakker.mobile_scanner.MobileScanner  stop ,dev.steenbakker.mobile_scanner.MobileScanner  textureEntry ,dev.steenbakker.mobile_scanner.MobileScanner  textureRegistry ,dev.steenbakker.mobile_scanner.MobileScanner  toggleTorch ,dev.steenbakker.mobile_scanner.MobileScanner  AlreadyStarted 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  AlreadyStopped 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  BarcodeScanning 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Bitmap 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Build 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ByteArrayOutputStream 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  CameraError 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Context 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
ContextCompat 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  DetectionSpeed 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Handler 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
ImageAnalysis 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
InputImage 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Looper 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Matrix 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MobileScannerStartParameters 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  NoCamera 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Preview 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ProcessCameraProvider 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Rect 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ResolutionSelector 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ResolutionStrategy 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Size 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Surface 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
TorchState 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  YuvToRgbConverter 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ZoomNotInRange 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ZoomWhenStopped 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  apply 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
captureOutput 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  data 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  defaultBarcodeScannerFactory 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getResolution 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
isNotEmpty 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  let 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  map 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
mapNotNull 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
mutableListOf 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
roundToInt 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  sorted 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  DisplayListener ;dev.steenbakker.mobile_scanner.MobileScanner.DisplayManager  SurfaceTextureEntry <dev.steenbakker.mobile_scanner.MobileScanner.TextureRegistry  BarcodeFormats 3dev.steenbakker.mobile_scanner.MobileScannerHandler  BarcodeScannerOptions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  CameraSelector 3dev.steenbakker.mobile_scanner.MobileScannerHandler  DetectionSpeed 3dev.steenbakker.mobile_scanner.MobileScannerHandler  File 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Handler 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Looper 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
MethodChannel 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
MobileScanner 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MobileScannerPermissions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Size 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Uri 3dev.steenbakker.mobile_scanner.MobileScannerHandler  activity 3dev.steenbakker.mobile_scanner.MobileScannerHandler  addPermissionListener 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImage 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImageErrorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImageSuccessCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzerResult 3dev.steenbakker.mobile_scanner.MobileScannerHandler  barcodeHandler 3dev.steenbakker.mobile_scanner.MobileScannerHandler  callback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  dispose 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
errorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  first 3dev.steenbakker.mobile_scanner.MobileScannerHandler  fromRawValue 3dev.steenbakker.mobile_scanner.MobileScannerHandler  mapOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
methodChannel 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
mobileScanner 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
mutableListOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  permissions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
resetScale 3dev.steenbakker.mobile_scanner.MobileScannerHandler  setScale 3dev.steenbakker.mobile_scanner.MobileScannerHandler  start 3dev.steenbakker.mobile_scanner.MobileScannerHandler  stop 3dev.steenbakker.mobile_scanner.MobileScannerHandler  to 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
toIntArray 3dev.steenbakker.mobile_scanner.MobileScannerHandler  toggleTorch 3dev.steenbakker.mobile_scanner.MobileScannerHandler  torchStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  updateScanWindow 3dev.steenbakker.mobile_scanner.MobileScannerHandler  zoomScaleStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Activity 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ActivityCompat 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Boolean 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  CAMERA_ACCESS_DENIED 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  CAMERA_ACCESS_DENIED_MESSAGE 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  "CAMERA_PERMISSIONS_REQUEST_ONGOING 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  *CAMERA_PERMISSIONS_REQUEST_ONGOING_MESSAGE 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  	Companion 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
ContextCompat 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Int 7dev.steenbakker.mobile_scanner.MobileScannerPermissions   MobileScannerPermissionsListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  PackageManager 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  REQUEST_CODE 7dev.steenbakker.mobile_scanner.MobileScannerPermissions   RequestPermissionsResultListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ResultCallback 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  String 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Unit 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  arrayOf 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  getPermissionListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  hasCameraPermission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  let 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  listener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ongoing 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
permission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  requestPermission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ActivityCompat Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  CAMERA_ACCESS_DENIED Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  CAMERA_ACCESS_DENIED_MESSAGE Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  "CAMERA_PERMISSIONS_REQUEST_ONGOING Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  *CAMERA_PERMISSIONS_REQUEST_ONGOING_MESSAGE Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
ContextCompat Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion   MobileScannerPermissionsListener Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  PackageManager Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  REQUEST_CODE Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  arrayOf Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  let Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  listener Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  ongoing Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
permission Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  onResult Fdev.steenbakker.mobile_scanner.MobileScannerPermissions.ResultCallback  MobileScannerPermissions ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  PackageManager ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  
alreadyCalled ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  isEmpty ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  resultCallback ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  BarcodeHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  MobileScannerHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  MobileScannerPermissions 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  activityPluginBinding 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  flutterPluginBinding 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  methodCallHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  onAttachedToActivity 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  onDetachedFromActivity 2dev.steenbakker.mobile_scanner.MobileScannerPlugin   RequestPermissionsResultListener -dev.steenbakker.mobile_scanner.PluginRegistry  SurfaceTextureEntry .dev.steenbakker.mobile_scanner.TextureRegistry  ALL_FORMATS &dev.steenbakker.mobile_scanner.objects  AZTEC &dev.steenbakker.mobile_scanner.objects  BarcodeFormats &dev.steenbakker.mobile_scanner.objects  CODABAR &dev.steenbakker.mobile_scanner.objects  CODE_128 &dev.steenbakker.mobile_scanner.objects  CODE_39 &dev.steenbakker.mobile_scanner.objects  CODE_93 &dev.steenbakker.mobile_scanner.objects  DATA_MATRIX &dev.steenbakker.mobile_scanner.objects  DetectionSpeed &dev.steenbakker.mobile_scanner.objects  Double &dev.steenbakker.mobile_scanner.objects  EAN_13 &dev.steenbakker.mobile_scanner.objects  EAN_8 &dev.steenbakker.mobile_scanner.objects  ITF &dev.steenbakker.mobile_scanner.objects  Int &dev.steenbakker.mobile_scanner.objects  Long &dev.steenbakker.mobile_scanner.objects  MobileScannerStartParameters &dev.steenbakker.mobile_scanner.objects  PDF417 &dev.steenbakker.mobile_scanner.objects  QR_CODE &dev.steenbakker.mobile_scanner.objects  UNKNOWN &dev.steenbakker.mobile_scanner.objects  UPC_A &dev.steenbakker.mobile_scanner.objects  UPC_E &dev.steenbakker.mobile_scanner.objects  com &dev.steenbakker.mobile_scanner.objects  ALL_FORMATS 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  AZTEC 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  BarcodeFormats 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODABAR 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_128 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_39 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_93 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  	Companion 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  DATA_MATRIX 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  EAN_13 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  EAN_8 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  ITF 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  Int 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  PDF417 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  QR_CODE 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UNKNOWN 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UPC_A 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UPC_E 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  com 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  fromRawValue 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  intValue 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  ALL_FORMATS ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  AZTEC ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODABAR ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_128 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_39 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_93 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  DATA_MATRIX ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  EAN_13 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  EAN_8 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  ITF ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  PDF417 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  QR_CODE ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UNKNOWN ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UPC_A ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UPC_E ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  com ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  fromRawValue ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  NORMAL 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  
NO_DUPLICATES 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  UNRESTRICTED 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  currentTorchState Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  height Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  id Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  numberOfCameras Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  width Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  
Allocation $dev.steenbakker.mobile_scanner.utils  AnnotationRetention $dev.steenbakker.mobile_scanner.utils  Bitmap $dev.steenbakker.mobile_scanner.utils  Boolean $dev.steenbakker.mobile_scanner.utils  	ByteArray $dev.steenbakker.mobile_scanner.utils  
ByteBuffer $dev.steenbakker.mobile_scanner.utils  Context $dev.steenbakker.mobile_scanner.utils  Element $dev.steenbakker.mobile_scanner.utils  Image $dev.steenbakker.mobile_scanner.utils  ImageFormat $dev.steenbakker.mobile_scanner.utils  ImageWrapper $dev.steenbakker.mobile_scanner.utils  Int $dev.steenbakker.mobile_scanner.utils  IntDef $dev.steenbakker.mobile_scanner.utils  PlaneWrapper $dev.steenbakker.mobile_scanner.utils  RenderScript $dev.steenbakker.mobile_scanner.utils  ScriptIntrinsicYuvToRGB $dev.steenbakker.mobile_scanner.utils  Synchronized $dev.steenbakker.mobile_scanner.utils  Type $dev.steenbakker.mobile_scanner.utils  
YuvByteBuffer $dev.steenbakker.mobile_scanner.utils  YuvToRgbConverter $dev.steenbakker.mobile_scanner.utils  YuvType $dev.steenbakker.mobile_scanner.utils  forEach $dev.steenbakker.mobile_scanner.utils  kotlin $dev.steenbakker.mobile_scanner.utils  require $dev.steenbakker.mobile_scanner.utils  until $dev.steenbakker.mobile_scanner.utils  Plane *dev.steenbakker.mobile_scanner.utils.Image  
ByteBuffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Image 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  ImageFormat 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  ImageWrapper 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Int 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  PlaneWrapper 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  YuvType 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  buffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
clipBuffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
removePadding 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  removePaddingCompact 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  removePaddingNotCompact 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  require 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  type 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  until 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Plane 8dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.Image  PlaneWrapper ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  height ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  require ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  u ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  v ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  width ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  y ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  buffer ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  height ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  pixelStride ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  	rowStride ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  width ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  
Allocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  	ByteArray 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Element 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  RenderScript 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  ScriptIntrinsicYuvToRGB 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Type 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  
YuvByteBuffer 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  bytes 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  inputAllocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  needCreateAllocations 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  outputAllocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  rs 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  scriptYuvToRgb 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  yuvBits 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  yuvToRgb 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  
annotation +dev.steenbakker.mobile_scanner.utils.kotlin  	Retention 6dev.steenbakker.mobile_scanner.utils.kotlin.annotation  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  textureRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  let Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  TextureRegistry io.flutter.view  SurfaceTextureEntry io.flutter.view.TextureRegistry  createSurfaceTexture io.flutter.view.TextureRegistry  id 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  release 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  surfaceTexture 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  id ,io.flutter.view.TextureRegistry.TextureEntry  release ,io.flutter.view.TextureRegistry.TextureEntry  ByteArrayOutputStream java.io  File java.io  Serializable java.io  toByteArray java.io.ByteArrayOutputStream  	Exception 	java.lang  IllegalArgumentException 	java.lang  Runnable 	java.lang  localizedMessage java.lang.Exception  toString java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  
ByteBuffer java.nio  capacity java.nio.Buffer  
isReadOnly java.nio.Buffer  limit java.nio.Buffer  position java.nio.Buffer  	remaining java.nio.Buffer  allocateDirect java.nio.ByteBuffer  capacity java.nio.ByteBuffer  	duplicate java.nio.ByteBuffer  get java.nio.ByteBuffer  isDirect java.nio.ByteBuffer  
isReadOnly java.nio.ByteBuffer  limit java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  rewind java.nio.ByteBuffer  slice java.nio.ByteBuffer  Executor java.util.concurrent  Array kotlin  	ByteArray kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function4 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  apply kotlin  arrayOf kotlin  let kotlin  map kotlin  require kotlin  to kotlin  toString 
kotlin.Any  get kotlin.Array  not kotlin.Boolean  size kotlin.ByteArray  	compareTo 
kotlin.Double  toFloat 
kotlin.Double  ALL_FORMATS kotlin.Enum  AZTEC kotlin.Enum  BarcodeFormats kotlin.Enum  CODABAR kotlin.Enum  CODE_128 kotlin.Enum  CODE_39 kotlin.Enum  CODE_93 kotlin.Enum  	Companion kotlin.Enum  DATA_MATRIX kotlin.Enum  EAN_13 kotlin.Enum  EAN_8 kotlin.Enum  ITF kotlin.Enum  Int kotlin.Enum  PDF417 kotlin.Enum  QR_CODE kotlin.Enum  UNKNOWN kotlin.Enum  UPC_A kotlin.Enum  UPC_E kotlin.Enum  com kotlin.Enum  ALL_FORMATS kotlin.Enum.Companion  AZTEC kotlin.Enum.Companion  CODABAR kotlin.Enum.Companion  CODE_128 kotlin.Enum.Companion  CODE_39 kotlin.Enum.Companion  CODE_93 kotlin.Enum.Companion  DATA_MATRIX kotlin.Enum.Companion  EAN_13 kotlin.Enum.Companion  EAN_8 kotlin.Enum.Companion  ITF kotlin.Enum.Companion  PDF417 kotlin.Enum.Companion  QR_CODE kotlin.Enum.Companion  UNKNOWN kotlin.Enum.Companion  UPC_A kotlin.Enum.Companion  UPC_E kotlin.Enum.Companion  com kotlin.Enum.Companion  
roundToInt kotlin.Float  times kotlin.Float  toDouble kotlin.Float  invoke kotlin.Function1  invoke kotlin.Function4  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  get kotlin.IntArray  isEmpty kotlin.IntArray  plus 
kotlin.String  to 
kotlin.String  toString 
kotlin.String  localizedMessage kotlin.Throwable  AnnotationRetention kotlin.annotation  	Retention kotlin.annotation  SOURCE %kotlin.annotation.AnnotationRetention  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  emptyMap kotlin.collections  first kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  sorted kotlin.collections  
toIntArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  sorted kotlin.collections.List  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  first kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  size kotlin.collections.MutableList  subList kotlin.collections.MutableList  
toIntArray kotlin.collections.MutableList  Synchronized 
kotlin.jvm  
roundToInt kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  first 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KFunction1 kotlin.reflect  Sequence kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  sorted kotlin.sequences  first kotlin.text  forEach kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
mapNotNull kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       