{"logs": [{"outputFile": "com.kryptopesa.kryptopesa_mobile.app-mergeDebugResources-60:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/res/values-fa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3519,3627,3784,3913,4016,4156,4282,4396,4653,4806,4916,5086,5217,5370,5526,5592,5660", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "3622,3779,3908,4011,4151,4277,4391,4493,4801,4911,5081,5212,5365,5521,5587,5655,5740"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,258,335,467,636,718", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "167,253,330,462,631,713,791"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5857,6111,7751,7828,8143,8312,8394", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "5919,6192,7823,7955,8307,8389,8467"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/res/values-fa/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4498", "endColumns": "154", "endOffsets": "4648"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5924,6197,6294,6405", "endColumns": "98,96,110,102", "endOffsets": "6018,6289,6400,6503"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a295c1332cd792405fffabf7b4bbac54/transformed/appcompat-1.2.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,7960", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,8037"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,255,372,500,623,773,890,1014,1111,1245,1383", "endColumns": "111,87,116,127,122,149,116,123,96,133,137,114", "endOffsets": "162,250,367,495,618,768,885,1009,1106,1240,1378,1493"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5745,6023,6508,6625,6753,6876,7026,7143,7267,7364,7498,7636", "endColumns": "111,87,116,127,122,149,116,123,96,133,137,114", "endOffsets": "5852,6106,6620,6748,6871,7021,7138,7262,7359,7493,7631,7746"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2795,2894,2996,3095,3195,3296,3402,8042", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "2889,2991,3090,3190,3291,3397,3514,8138"}}]}]}