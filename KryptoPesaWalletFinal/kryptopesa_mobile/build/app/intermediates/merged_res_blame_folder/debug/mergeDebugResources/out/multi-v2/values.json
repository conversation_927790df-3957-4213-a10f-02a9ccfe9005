{"logs": [{"outputFile": "com.kryptopesa.kryptopesa_mobile.app-mergeDebugResources-60:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "363,380,408,3076,3081", "startColumns": "4,4,4,4,4", "startOffsets": "21172,21986,23461,174693,174863", "endLines": "363,380,408,3080,3084", "endColumns": "56,64,63,24,24", "endOffsets": "21224,22046,23520,174858,175007"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9afb31c75cdd321eb1d7dc2fef0c4e5a/transformed/work-runtime-2.8.1/res/values/values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "96,97,98,99", "startColumns": "4,4,4,4", "startOffsets": "3322,3387,3457,3521", "endColumns": "64,69,63,60", "endOffsets": "3382,3452,3516,3577"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "407", "startColumns": "4", "startOffsets": "23411", "endColumns": "49", "endOffsets": "23456"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a5e2f5b04aeb5754451bc27c5d0bc62c/transformed/jetified-firebase-messaging-23.4.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "478", "startColumns": "4", "startOffsets": "29339", "endColumns": "81", "endOffsets": "29416"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values/values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,113,276,470,473,479,480,481,482,483,484,485,486,487,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4562,15727,28825,29007,29421,29510,29609,29717,29814,29902,30002,30072,30169,30279", "endLines": "5,7,10,14,33,113,276,470,473,479,480,481,482,483,484,485,486,487,488", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4613,15775,28896,29062,29505,29604,29712,29809,29897,29997,30067,30164,30274,30363"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "273,274,275,285,286,287,367,3512", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15564,15623,15671,16437,16512,16588,21350,188284", "endLines": "273,274,275,285,286,287,367,3531", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15618,15666,15722,16507,16583,16655,21411,189074"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "406", "startColumns": "4", "startOffsets": "23357", "endColumns": "53", "endOffsets": "23406"}}, {"source": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "1590,1594", "startColumns": "4,4", "startOffsets": "100626,100807", "endLines": "1593,1596", "endColumns": "12,12", "endOffsets": "100802,100971"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/185f2479ab24942c0bba65b9ff947d79/transformed/jetified-appcompat-resources-1.2.0/res/values/values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2308,2324,2330,3650,3666", "startColumns": "4,4,4,4,4", "startOffsets": "146863,147288,147466,192893,193304", "endLines": "2323,2329,2339,3665,3669", "endColumns": "24,24,24,24,24", "endOffsets": "147283,147461,147745,193299,193426"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,362,2287,2293,3611,3619,3634", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,21112,145993,146188,191343,191625,192239", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,362,2292,2297,3618,3633,3649", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,21167,146183,146341,191620,192234,192888"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "95,163,304,305,306,307,308,309,310,371,372,373,414,415,471,474,489,490,495,496,497,1573,1757,1760,1766,1772,1775,1781,1785,1788,1795,1801,1804,1810,1815,1820,1827,1829,1835,1841,1849,1854,1861,1866,1872,1876,1883,1887,1893,1899,1902,1906,1907,2826,2841,2980,3018,3160,3348,3366,3430,3440,3450,3457,3463,3567,3736,3753", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3249,7973,17686,17750,17805,17873,17940,18005,18062,21529,21577,21625,23836,23899,28901,29067,30368,30412,30676,30815,30865,99188,112926,113031,113276,113614,113760,114100,114312,114475,114882,115220,115343,115682,115921,116178,116549,116609,116947,117233,117682,117974,118362,118667,119011,119256,119586,119793,120061,120334,120478,120679,120726,163522,164045,170831,172132,177074,182984,183612,185537,185819,186124,186386,186646,190162,196457,196987", "endLines": "95,163,304,305,306,307,308,309,310,371,372,373,414,415,471,474,489,492,495,496,497,1589,1759,1765,1771,1774,1780,1784,1787,1794,1800,1803,1809,1814,1819,1826,1828,1834,1840,1848,1853,1860,1865,1871,1875,1882,1886,1892,1898,1901,1905,1906,1907,2830,2851,2999,3021,3169,3355,3429,3439,3449,3456,3462,3505,3579,3752,3769", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3317,8037,17745,17800,17868,17935,18000,18057,18114,21572,21620,21681,23894,23957,28934,29119,30407,30547,30810,30860,30908,100621,113026,113271,113609,113755,114095,114307,114470,114877,115215,115338,115677,115916,116173,116544,116604,116942,117228,117677,117969,118357,118662,119006,119251,119581,119788,120056,120329,120473,120674,120721,120777,163702,164441,171555,172276,177401,183227,185532,185814,186119,186381,186641,188064,190609,196982,197550"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "121,122,123,124,262,263,472,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5189,5247,5313,5376,14760,14831,28939,29124,29191,29270", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "5242,5308,5371,5433,14826,14898,29002,29186,29265,29334"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "23254", "endColumns": "42", "endOffsets": "23292"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "413,460", "startColumns": "4,4", "startOffsets": "23768,27532", "endColumns": "67,166", "endOffsets": "23831,27694"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f84db7003533a22de0405c5251ecb704/transformed/media-1.1.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "162,168,174,311,312,313,314,411,2024,2026,2027,2032,2034", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7884,8342,8745,18119,18172,18225,18278,23651,130069,130245,130367,130629,130824", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "7968,8408,8813,18167,18220,18273,18326,23706,130130,130362,130423,130690,130886"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2180,2890,2896", "startColumns": "4,4,4,4", "startOffsets": "1213,142325,166104,166315", "endLines": "35,2182,2895,2979", "endColumns": "60,12,24,24", "endOffsets": "1269,142465,166310,170826"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "129,130,131,132,133,134,135,136,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,3170,3580", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5684,5774,5854,5944,6034,6114,6195,6275,26492,26597,26778,26903,27010,27190,27313,27429,27699,27887,27992,28173,28298,28473,28621,28684,28746,177406,190614", "endLines": "129,130,131,132,133,134,135,136,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,3182,3598", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5769,5849,5939,6029,6109,6190,6270,6350,26592,26773,26898,27005,27185,27308,27424,27527,27882,27987,28168,28293,28468,28616,28679,28741,28820,177716,191026"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/93eeca70efd8419049cd49df8af72af1/transformed/jetified-activity-1.9.3/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "376,405", "startColumns": "4,4", "startOffsets": "21782,23297", "endColumns": "41,59", "endOffsets": "21819,23352"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a295c1332cd792405fffabf7b4bbac54/transformed/appcompat-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3656,3728,3800,3873,3930,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3723,3795,3868,3925,3983,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "36,59,60,91,92,93,94,100,101,102,103,104,105,106,109,110,111,112,115,116,117,118,119,120,125,126,137,138,139,140,141,142,143,144,146,147,148,149,150,151,152,153,154,155,156,157,158,159,164,165,166,167,169,170,171,172,173,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,271,272,277,278,279,280,281,282,283,315,316,317,318,319,320,321,322,358,359,360,361,366,374,375,381,403,409,410,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,493,498,499,506,507,508,509,517,518,522,526,530,535,541,548,552,556,561,565,569,573,577,581,585,591,595,601,605,611,615,620,624,627,631,637,641,647,651,657,660,664,668,672,676,680,681,682,683,686,689,692,695,699,700,701,702,703,706,708,710,712,717,718,722,728,732,733,735,746,747,751,757,761,762,763,767,794,798,799,803,831,1000,1026,1195,1221,1252,1260,1266,1280,1302,1307,1312,1322,1331,1340,1344,1351,1359,1366,1367,1376,1379,1382,1386,1390,1394,1397,1398,1403,1408,1418,1423,1430,1436,1437,1440,1444,1449,1451,1453,1456,1459,1461,1465,1468,1475,1478,1481,1485,1487,1491,1493,1495,1497,1501,1509,1517,1529,1535,1544,1547,1558,1561,1562,1567,1568,1597,1666,1736,1737,1747,1756,1908,1910,1914,1917,1920,1923,1926,1929,1932,1935,1939,1942,1945,1948,1952,1955,1959,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1985,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1999,2000,2002,2004,2005,2007,2008,2009,2010,2011,2012,2014,2015,2016,2017,2018,2035,2037,2039,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2055,2056,2057,2058,2059,2060,2061,2063,2067,2071,2072,2073,2074,2075,2076,2080,2081,2082,2083,2085,2087,2089,2091,2093,2094,2095,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2116,2117,2118,2119,2121,2123,2124,2126,2127,2129,2131,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2146,2147,2148,2149,2151,2152,2153,2154,2155,2157,2159,2161,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2183,2258,2261,2264,2267,2281,2298,2340,2369,2396,2405,2467,2831,2862,3000,3124,3148,3154,3183,3204,3328,3356,3362,3506,3532,3599,3670,3770,3790,3845,3857,3883", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3187,3582,3646,3716,3777,3852,3928,4005,4243,4328,4410,4486,4663,4740,4818,4924,5030,5109,5438,5495,6355,6429,6504,6569,6635,6695,6756,6828,6945,7012,7080,7139,7198,7257,7316,7375,7429,7483,7536,7590,7644,7698,8042,8116,8195,8268,8413,8485,8557,8630,8687,8818,8892,8966,9041,9113,9186,9256,9327,9387,9448,9517,9586,9656,9730,9806,9870,9947,10023,10100,10165,10234,10311,10386,10455,10523,10600,10666,10727,10824,10889,10958,11057,11128,11187,11245,11302,11361,11425,11496,11568,11640,11712,11784,11851,11919,11987,12046,12109,12173,12263,12354,12414,12480,12547,12613,12683,12747,12800,12867,12928,12995,13108,13166,13229,13294,13359,13434,13507,13579,13628,13689,13750,13811,13873,13937,14001,14065,14130,14193,14253,14314,14380,14439,14499,14561,14632,14692,15391,15477,15780,15870,15957,16045,16127,16210,16300,18331,18383,18441,18486,18552,18616,18673,18730,20907,20964,21012,21061,21316,21686,21733,22051,23222,23525,23589,23711,24032,24106,24176,24254,24308,24378,24463,24511,24557,24618,24681,24747,24811,24882,24945,25010,25074,25135,25196,25248,25321,25395,25464,25539,25613,25687,25828,30552,30913,30991,31381,31469,31565,31655,32237,32326,32573,32854,33106,33391,33784,34261,34483,34705,34981,35208,35438,35668,35898,36128,36355,36774,37000,37425,37655,38083,38302,38585,38793,38924,39151,39577,39802,40229,40450,40875,40995,41271,41572,41896,42187,42501,42638,42769,42874,43116,43283,43487,43695,43966,44078,44190,44295,44412,44626,44772,44912,44998,45346,45434,45680,46098,46347,46429,46527,47119,47219,47471,47895,48150,48244,48333,48570,50594,50836,50938,51191,53347,63788,65304,75843,77371,79128,79754,80174,81235,82500,82756,82992,83539,84033,84638,84836,85416,85980,86355,86473,87011,87168,87364,87637,87893,88063,88204,88268,88633,89000,89676,89940,90278,90631,90725,90911,91217,91479,91604,91731,91970,92181,92300,92493,92670,93125,93306,93428,93687,93800,93987,94089,94196,94325,94600,95108,95604,96481,96775,97345,97494,98226,98398,98482,98818,98910,100976,106222,111611,111673,112251,112835,120782,120895,121124,121284,121436,121607,121773,121942,122109,122272,122515,122685,122858,123029,123303,123502,123707,124037,124121,124217,124313,124411,124511,124613,124715,124817,124919,125021,125121,125217,125329,125458,125581,125712,125843,125941,126055,126149,126289,126423,126519,126631,126731,126847,126943,127055,127155,127295,127431,127595,127725,127883,128033,128174,128318,128453,128565,128715,128843,128971,129107,129239,129369,129499,129611,130891,131037,131181,131319,131385,131475,131551,131655,131745,131847,131955,132063,132163,132243,132335,132433,132543,132595,132673,132779,132871,132975,133085,133207,133370,133527,133607,133707,133797,133907,133997,134238,134332,134438,134530,134630,134742,134856,134972,135088,135182,135296,135408,135510,135630,135752,135834,135938,136058,136184,136282,136376,136464,136576,136692,136814,136926,137101,137217,137303,137395,137507,137631,137698,137824,137892,138020,138164,138292,138361,138456,138571,138684,138783,138892,139003,139114,139215,139320,139420,139550,139641,139764,139858,139970,140056,140160,140256,140344,140462,140566,140670,140796,140884,140992,141092,141182,141292,141376,141478,141562,141616,141680,141786,141872,141982,142066,142470,145086,145204,145319,145399,145760,146346,147750,149094,150455,150843,153618,163707,164747,171560,175861,176612,176874,177721,178100,182378,183232,183461,188069,189079,191031,193431,197555,198299,200430,200770,202081", "endLines": "36,59,60,91,92,93,94,100,101,102,103,104,105,106,109,110,111,112,115,116,117,118,119,120,125,126,137,138,139,140,141,142,143,144,146,147,148,149,150,151,152,153,154,155,156,157,158,159,164,165,166,167,169,170,171,172,173,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,271,272,277,278,279,280,281,282,283,315,316,317,318,319,320,321,322,358,359,360,361,366,374,375,381,403,409,410,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,493,498,499,506,507,508,516,517,521,525,529,534,540,547,551,555,560,564,568,572,576,580,584,590,594,600,604,610,614,619,623,626,630,636,640,646,650,656,659,663,667,671,675,679,680,681,682,685,688,691,694,698,699,700,701,702,705,707,709,711,716,717,721,727,731,732,734,745,746,750,756,760,761,762,766,793,797,798,802,830,999,1025,1194,1220,1251,1259,1265,1279,1301,1306,1311,1321,1330,1339,1343,1350,1358,1365,1366,1375,1378,1381,1385,1389,1393,1396,1397,1402,1407,1417,1422,1429,1435,1436,1439,1443,1448,1450,1452,1455,1458,1460,1464,1467,1474,1477,1480,1484,1486,1490,1492,1494,1496,1500,1508,1516,1528,1534,1543,1546,1557,1560,1561,1566,1567,1572,1665,1735,1736,1746,1755,1756,1909,1913,1916,1919,1922,1925,1928,1931,1934,1938,1941,1944,1947,1951,1954,1958,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1984,1986,1987,1988,1989,1990,1991,1992,1993,1995,1996,1998,1999,2001,2003,2004,2006,2007,2008,2009,2010,2011,2013,2014,2015,2016,2017,2018,2036,2038,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2054,2055,2056,2057,2058,2059,2060,2062,2066,2070,2071,2072,2073,2074,2075,2079,2080,2081,2082,2084,2086,2088,2090,2092,2093,2094,2095,2097,2099,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2115,2116,2117,2118,2120,2122,2123,2125,2126,2128,2130,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2145,2146,2147,2148,2150,2151,2152,2153,2154,2156,2158,2160,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2257,2260,2263,2266,2280,2286,2307,2368,2395,2404,2466,2825,2834,2889,3017,3147,3153,3159,3203,3327,3347,3361,3365,3511,3566,3610,3735,3789,3844,3856,3882,3889", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3182,3244,3641,3711,3772,3847,3923,4000,4078,4323,4405,4481,4557,4735,4813,4919,5025,5104,5184,5490,5548,6424,6499,6564,6630,6690,6751,6823,6896,7007,7075,7134,7193,7252,7311,7370,7424,7478,7531,7585,7639,7693,7747,8111,8190,8263,8337,8480,8552,8625,8682,8740,8887,8961,9036,9108,9181,9251,9322,9382,9443,9512,9581,9651,9725,9801,9865,9942,10018,10095,10160,10229,10306,10381,10450,10518,10595,10661,10722,10819,10884,10953,11052,11123,11182,11240,11297,11356,11420,11491,11563,11635,11707,11779,11846,11914,11982,12041,12104,12168,12258,12349,12409,12475,12542,12608,12678,12742,12795,12862,12923,12990,13103,13161,13224,13289,13354,13429,13502,13574,13623,13684,13745,13806,13868,13932,13996,14060,14125,14188,14248,14309,14375,14434,14494,14556,14627,14687,14755,15472,15559,15865,15952,16040,16122,16205,16295,16386,18378,18436,18481,18547,18611,18668,18725,18779,20959,21007,21056,21107,21345,21728,21777,22092,23249,23584,23646,23763,24101,24171,24249,24303,24373,24458,24506,24552,24613,24676,24742,24806,24877,24940,25005,25069,25130,25191,25243,25316,25390,25459,25534,25608,25682,25823,25893,30600,30986,31076,31464,31560,31650,32232,32321,32568,32849,33101,33386,33779,34256,34478,34700,34976,35203,35433,35663,35893,36123,36350,36769,36995,37420,37650,38078,38297,38580,38788,38919,39146,39572,39797,40224,40445,40870,40990,41266,41567,41891,42182,42496,42633,42764,42869,43111,43278,43482,43690,43961,44073,44185,44290,44407,44621,44767,44907,44993,45341,45429,45675,46093,46342,46424,46522,47114,47214,47466,47890,48145,48239,48328,48565,50589,50831,50933,51186,53342,63783,65299,75838,77366,79123,79749,80169,81230,82495,82751,82987,83534,84028,84633,84831,85411,85975,86350,86468,87006,87163,87359,87632,87888,88058,88199,88263,88628,88995,89671,89935,90273,90626,90720,90906,91212,91474,91599,91726,91965,92176,92295,92488,92665,93120,93301,93423,93682,93795,93982,94084,94191,94320,94595,95103,95599,96476,96770,97340,97489,98221,98393,98477,98813,98905,99183,106217,111606,111668,112246,112830,112921,120890,121119,121279,121431,121602,121768,121937,122104,122267,122510,122680,122853,123024,123298,123497,123702,124032,124116,124212,124308,124406,124506,124608,124710,124812,124914,125016,125116,125212,125324,125453,125576,125707,125838,125936,126050,126144,126284,126418,126514,126626,126726,126842,126938,127050,127150,127290,127426,127590,127720,127878,128028,128169,128313,128448,128560,128710,128838,128966,129102,129234,129364,129494,129606,129746,131032,131176,131314,131380,131470,131546,131650,131740,131842,131950,132058,132158,132238,132330,132428,132538,132590,132668,132774,132866,132970,133080,133202,133365,133522,133602,133702,133792,133902,133992,134233,134327,134433,134525,134625,134737,134851,134967,135083,135177,135291,135403,135505,135625,135747,135829,135933,136053,136179,136277,136371,136459,136571,136687,136809,136921,137096,137212,137298,137390,137502,137626,137693,137819,137887,138015,138159,138287,138356,138451,138566,138679,138778,138887,138998,139109,139210,139315,139415,139545,139636,139759,139853,139965,140051,140155,140251,140339,140457,140561,140665,140791,140879,140987,141087,141177,141287,141371,141473,141557,141611,141675,141781,141867,141977,142061,142181,145081,145199,145314,145394,145755,145988,146858,149089,150450,150838,153613,163517,163837,166099,172127,176607,176869,177069,178095,182373,182979,183456,183607,188279,190157,191338,196452,198294,200425,200765,202076,202279"}}, {"source": "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/local_auth_android/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "114,145,284,288,500", "startColumns": "4,4,4,4,4", "startOffsets": "4618,6901,16391,16660,31081", "endLines": "114,145,284,288,505", "endColumns": "44,43,45,47,10", "endOffsets": "4658,6940,16432,16703,31376"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7d88f386947ff6b0f3308f144354f040/transformed/jetified-sentry-android-replay-7.22.4/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "59", "endOffsets": "110"}, "to": {"startLines": "379", "startColumns": "4", "startOffsets": "21926", "endColumns": "59", "endOffsets": "21981"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "444", "startColumns": "4", "startOffsets": "25898", "endColumns": "82", "endOffsets": "25976"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,107,108,127,128,160,161,264,265,266,267,268,269,270,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,368,369,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,445,446,447,448,449,450,451,494,2019,2020,2025,2028,2033,2178,2179,2835,2852,3022,3055,3085,3118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,4083,4155,5553,5618,7752,7821,14903,14973,15041,15113,15183,15244,15318,16708,16769,16830,16892,16956,17018,17079,17147,17247,17307,17373,17446,17515,17572,17624,18784,18856,18932,18997,19056,19115,19175,19235,19295,19355,19415,19475,19535,19595,19655,19715,19774,19834,19894,19954,20014,20074,20134,20194,20254,20314,20374,20433,20493,20553,20612,20671,20730,20789,20848,21416,21451,22097,22152,22215,22270,22328,22386,22447,22510,22567,22618,22668,22729,22786,22852,22886,22921,23962,25981,26048,26120,26189,26258,26332,26404,30605,129751,129868,130135,130428,130695,142186,142258,163842,164446,172281,174012,175012,175694", "endLines": "61,107,108,127,128,160,161,264,265,266,267,268,269,270,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,368,369,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,445,446,447,448,449,450,451,494,2019,2023,2025,2031,2033,2178,2179,2840,2861,3054,3075,3117,3123", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,4150,4238,5613,5679,7816,7879,14968,15036,15108,15178,15239,15313,15386,16764,16825,16887,16951,17013,17074,17142,17242,17302,17368,17441,17510,17567,17619,17681,18851,18927,18992,19051,19110,19170,19230,19290,19350,19410,19470,19530,19590,19650,19710,19769,19829,19889,19949,20009,20069,20129,20189,20249,20309,20369,20428,20488,20548,20607,20666,20725,20784,20843,20902,21446,21481,22147,22210,22265,22323,22381,22442,22505,22562,22613,22663,22724,22781,22847,22881,22916,22951,24027,26043,26115,26184,26253,26327,26399,26487,30671,129863,130064,130240,130624,130819,142253,142320,164040,164742,174007,174688,175689,175856"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "364,365,370,377,378,398,399,400,401,402", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21229,21269,21486,21824,21879,22956,23010,23062,23111,23172", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21264,21311,21524,21874,21921,23005,23057,23106,23167,23217"}}]}]}