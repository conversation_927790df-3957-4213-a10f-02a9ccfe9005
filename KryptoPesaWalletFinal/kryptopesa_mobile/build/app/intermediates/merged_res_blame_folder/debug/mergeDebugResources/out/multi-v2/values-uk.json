{"logs": [{"outputFile": "com.kryptopesa.kryptopesa_mobile.app-mergeDebugResources-60:/values-uk/values-uk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5782,6073,6589,6716,6847,6988,7099,7226,7360,7459,7589,7721", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "5884,6161,6711,6842,6983,7094,7221,7355,7454,7584,7716,7841"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7976d4e64729cb9c47971e21b0850b04/transformed/jetified-play-services-base-18.1.0/res/values-uk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3664,3827,3954,4064,4218,4347,4462,4713,4881,4987,5149,5274,5421,5563,5633,5694", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "3659,3822,3949,4059,4213,4342,4457,4562,4876,4982,5144,5269,5416,5558,5628,5689,5777"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a295c1332cd792405fffabf7b4bbac54/transformed/appcompat-1.2.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,8069", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,8146"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5963,6254,6361,6481", "endColumns": "109,106,119,107", "endOffsets": "6068,6356,6476,6584"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9d4e1de4e870e893108c546e2600c23f/transformed/jetified-play-services-basement-18.3.0/res/values-uk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4567", "endColumns": "145", "endOffsets": "4708"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5889,6166,7846,7927,8252,8421,8506", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "5958,6249,7922,8064,8416,8501,8584"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values-uk/values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2829,2929,3031,3132,3233,3338,3443,8151", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "2924,3026,3127,3228,3333,3438,3551,8247"}}]}]}