<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:barcode-scanning:17.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/8.12/transforms/732b5d8c2ef2d0f0c0675f1c83378f6c/transformed/jetified-barcode-scanning-17.2.0/assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="/Users/<USER>/.gradle/caches/8.12/transforms/732b5d8c2ef2d0f0c0675f1c83378f6c/transformed/jetified-barcode-scanning-17.2.0/assets/mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="/Users/<USER>/.gradle/caches/8.12/transforms/732b5d8c2ef2d0f0c0675f1c83378f6c/transformed/jetified-barcode-scanning-17.2.0/assets/mlkit_barcode_models/oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="/Users/<USER>/.gradle/caches/8.12/transforms/732b5d8c2ef2d0f0c0675f1c83378f6c/transformed/jetified-barcode-scanning-17.2.0/assets/mlkit_barcode_models/oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":workmanager" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/workmanager/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/sqflite_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/permission_handler_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/path_provider_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/flutter_secure_storage/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/flutter_plugin_android_lifecycle/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":flutter_keychain" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/flutter_keychain/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/firebase_core/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/firebase_messaging/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/connectivity_plus/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/url_launcher_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/local_auth_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/image_picker_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/flutter_local_notifications/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/file_picker/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/shared_preferences_android/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/share_plus/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/package_info_plus/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":sentry_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/sentry_flutter/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":mobile_scanner" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/mobile_scanner/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/device_info_plus/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/android/app/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/app/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>