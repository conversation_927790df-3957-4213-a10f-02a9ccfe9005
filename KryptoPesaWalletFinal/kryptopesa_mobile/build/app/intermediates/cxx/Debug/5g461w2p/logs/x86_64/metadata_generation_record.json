[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86_64", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/.cxx/Debug/5g461w2p/x86_64/android_gradle_build.json' was up-to-date", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]