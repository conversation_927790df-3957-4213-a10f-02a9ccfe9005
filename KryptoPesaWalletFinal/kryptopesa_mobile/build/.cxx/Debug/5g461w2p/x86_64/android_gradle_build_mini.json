{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/.cxx/Debug/5g461w2p/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/KryptoPesaWalletFinal/kryptopesa_mobile/build/.cxx/Debug/5g461w2p/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}