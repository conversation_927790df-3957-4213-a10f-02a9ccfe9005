name: kryptopesa_mobile
description: "KryptoPesa - P2P Cryptocurrency Trading Platform for East Africa"
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.1.2

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Navigation
  go_router: ^14.2.7

  # HTTP & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # WebSocket & Real-time
  socket_io_client: ^2.0.3+1
  web_socket_channel: ^2.4.5

  # Local Storage & Database
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.3+1
  path: ^1.9.0

  # Security & Authentication
  local_auth: ^2.3.0
  local_auth_android: ^1.0.43
  local_auth_ios: ^1.0.21
  crypto: ^3.0.3
  encrypt: ^5.0.3
  flutter_secure_storage: ^9.2.2

  # Blockchain & Crypto
  web3dart: ^2.7.3
  bip39: ^1.0.6
  ed25519_hd_key: ^2.2.0
  hex: ^0.2.0

  # Networking & Connectivity
  connectivity_plus: ^6.0.5
  internet_connection_checker: ^1.0.0+1

  # Device Features
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # Push Notifications
  firebase_core: ^2.32.0
  firebase_messaging: ^14.9.4
  flutter_local_notifications: ^17.2.2

  # File Handling & Media
  image_picker: ^1.1.2
  file_picker: ^8.0.7
  path_provider: ^2.1.4

  # QR Code
  mobile_scanner: ^5.0.1
  qr_flutter: ^4.1.0

  # Utilities
  intl: ^0.18.1
  timeago: ^3.6.1
  url_launcher: ^6.3.0
  share_plus: ^9.0.0

  # Logging & Analytics
  logger: ^2.4.0

  # Security & Biometrics (already defined above)
  pointycastle: ^3.9.1

  # Offline & Database
  # workmanager: ^0.5.2  # Temporarily disabled due to compatibility issues
  uuid: ^4.5.1

  # QR Code & Scanning (already defined above)
  sentry_flutter: ^8.9.0

  # Development
  flutter_dotenv: ^5.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.12
  retrofit_generator: ^8.1.2
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - .env

  # Fonts (temporarily commented out)
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
