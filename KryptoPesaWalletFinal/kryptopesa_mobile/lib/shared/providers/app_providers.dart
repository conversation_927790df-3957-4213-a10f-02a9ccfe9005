import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../features/auth/providers/auth_provider.dart';
// import '../../features/wallet/providers/wallet_provider.dart';
import '../../features/trading/providers/trading_provider.dart';
import '../../features/chat/providers/chat_provider.dart';
import '../../features/profile/providers/profile_provider.dart';
import '../../core/services/navigation_service.dart';

/// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeIndex = prefs.getInt('theme_mode') ?? 0;
    state = ThemeMode.values[themeModeIndex];
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', themeMode.index);
  }
}

/// Router Provider
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  
  return GoRouter(
    initialLocation: authState.isAuthenticated ? '/home' : '/onboarding',
    redirect: (context, state) {
      final isAuthenticated = ref.read(authProvider).isAuthenticated;
      final isOnboarding = state.matchedLocation.startsWith('/onboarding');
      final isAuth = state.matchedLocation.startsWith('/auth');

      // If not authenticated and not on auth/onboarding pages, redirect to onboarding
      if (!isAuthenticated && !isOnboarding && !isAuth) {
        return '/onboarding';
      }

      // If authenticated and on auth/onboarding pages, redirect to home
      if (isAuthenticated && (isOnboarding || isAuth)) {
        return '/home';
      }

      return null;
    },
    routes: [
      // Onboarding Routes
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: '/auth/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/auth/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/auth/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/auth/verify-email',
        builder: (context, state) => const VerifyEmailScreen(),
      ),
      
      // Main App Routes
      ShellRoute(
        builder: (context, state, child) => MainNavigationWrapper(child: child),
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: '/wallet',
            builder: (context, state) => const WalletScreen(),
            routes: [
              GoRoute(
                path: '/send',
                builder: (context, state) => const SendCryptoScreen(),
              ),
              GoRoute(
                path: '/receive',
                builder: (context, state) => const ReceiveCryptoScreen(),
              ),
              GoRoute(
                path: '/transaction/:id',
                builder: (context, state) => TransactionDetailScreen(
                  transactionId: state.pathParameters['id']!,
                ),
              ),
            ],
          ),
          GoRoute(
            path: '/trading',
            builder: (context, state) => const TradingScreen(),
            routes: [
              GoRoute(
                path: '/create-offer',
                builder: (context, state) => const CreateOfferScreen(),
              ),
              GoRoute(
                path: '/offer/:id',
                builder: (context, state) => OfferDetailScreen(
                  offerId: state.pathParameters['id']!,
                ),
              ),
              GoRoute(
                path: '/trade/:id',
                builder: (context, state) => TradeDetailScreen(
                  tradeId: state.pathParameters['id']!,
                ),
              ),
            ],
          ),
          GoRoute(
            path: '/chat',
            builder: (context, state) => const ChatListScreen(),
            routes: [
              GoRoute(
                path: '/:id',
                builder: (context, state) => ChatScreen(
                  chatId: state.pathParameters['id']!,
                ),
              ),
            ],
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
            routes: [
              GoRoute(
                path: '/edit',
                builder: (context, state) => const EditProfileScreen(),
              ),
              GoRoute(
                path: '/verification',
                builder: (context, state) => const VerificationScreen(),
              ),
              GoRoute(
                path: '/reputation',
                builder: (context, state) => const ReputationScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsScreen(),
            routes: [
              GoRoute(
                path: '/security',
                builder: (context, state) => const SecuritySettingsScreen(),
              ),
              GoRoute(
                path: '/notifications',
                builder: (context, state) => const NotificationSettingsScreen(),
              ),
              GoRoute(
                path: '/privacy',
                builder: (context, state) => const PrivacySettingsScreen(),
              ),
            ],
          ),
        ],
      ),
      
      // Standalone Routes
      GoRoute(
        path: '/qr-scanner',
        builder: (context, state) => const QRScannerScreen(),
      ),
      GoRoute(
        path: '/biometric-setup',
        builder: (context, state) => const BiometricSetupScreen(),
      ),
      GoRoute(
        path: '/backup-wallet',
        builder: (context, state) => const BackupWalletScreen(),
      ),
      GoRoute(
        path: '/restore-wallet',
        builder: (context, state) => const RestoreWalletScreen(),
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
});

/// Connectivity Provider
final connectivityProvider = StreamProvider<bool>((ref) {
  return ConnectivityService.connectivityStream;
});

/// App Lifecycle Provider
final appLifecycleProvider = StateNotifierProvider<AppLifecycleNotifier, AppLifecycleState>(
  (ref) => AppLifecycleNotifier(),
);

class AppLifecycleNotifier extends StateNotifier<AppLifecycleState> with WidgetsBindingObserver {
  AppLifecycleNotifier() : super(AppLifecycleState.resumed) {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    this.state = state;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}

// Placeholder imports - these will be created in subsequent files
class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Onboarding')));
}

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Login')));
}

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Register')));
}

class ForgotPasswordScreen extends StatelessWidget {
  const ForgotPasswordScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Forgot Password')));
}

class VerifyEmailScreen extends StatelessWidget {
  const VerifyEmailScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Verify Email')));
}

class MainNavigationWrapper extends StatelessWidget {
  final Widget child;
  const MainNavigationWrapper({super.key, required this.child});
  @override
  Widget build(BuildContext context) => Scaffold(body: child);
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Home')));
}

class WalletScreen extends StatelessWidget {
  const WalletScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Wallet')));
}

class SendCryptoScreen extends StatelessWidget {
  const SendCryptoScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Send Crypto')));
}

class ReceiveCryptoScreen extends StatelessWidget {
  const ReceiveCryptoScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Receive Crypto')));
}

class TransactionDetailScreen extends StatelessWidget {
  final String transactionId;
  const TransactionDetailScreen({super.key, required this.transactionId});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Transaction: $transactionId')));
}

class TradingScreen extends StatelessWidget {
  const TradingScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Trading')));
}

class CreateOfferScreen extends StatelessWidget {
  const CreateOfferScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Create Offer')));
}

class OfferDetailScreen extends StatelessWidget {
  final String offerId;
  const OfferDetailScreen({super.key, required this.offerId});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Offer: $offerId')));
}

class TradeDetailScreen extends StatelessWidget {
  final String tradeId;
  const TradeDetailScreen({super.key, required this.tradeId});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Trade: $tradeId')));
}

class ChatListScreen extends StatelessWidget {
  const ChatListScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Chat List')));
}

class ChatScreen extends StatelessWidget {
  final String chatId;
  const ChatScreen({super.key, required this.chatId});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Chat: $chatId')));
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Profile')));
}

class EditProfileScreen extends StatelessWidget {
  const EditProfileScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Edit Profile')));
}

class VerificationScreen extends StatelessWidget {
  const VerificationScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Verification')));
}

class ReputationScreen extends StatelessWidget {
  const ReputationScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Reputation')));
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Settings')));
}

class SecuritySettingsScreen extends StatelessWidget {
  const SecuritySettingsScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Security Settings')));
}

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Notification Settings')));
}

class PrivacySettingsScreen extends StatelessWidget {
  const PrivacySettingsScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Privacy Settings')));
}

class QRScannerScreen extends StatelessWidget {
  const QRScannerScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('QR Scanner')));
}

class BiometricSetupScreen extends StatelessWidget {
  const BiometricSetupScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Biometric Setup')));
}

class BackupWalletScreen extends StatelessWidget {
  const BackupWalletScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Backup Wallet')));
}

class RestoreWalletScreen extends StatelessWidget {
  const RestoreWalletScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Restore Wallet')));
}

class ErrorScreen extends StatelessWidget {
  final Exception? error;
  const ErrorScreen({super.key, this.error});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Error: ${error.toString()}')));
}

// Placeholder services
class ConnectivityService {
  static Stream<bool> get connectivityStream => Stream.value(true);
}
