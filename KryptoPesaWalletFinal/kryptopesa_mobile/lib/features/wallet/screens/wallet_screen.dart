import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_dialog.dart';
import '../providers/wallet_provider.dart';
import '../widgets/portfolio_card.dart';
import '../widgets/crypto_balance_card.dart';
import '../widgets/quick_actions_row.dart';
import '../widgets/recent_transactions_list.dart';

class WalletScreen extends ConsumerStatefulWidget {
  const WalletScreen({super.key});

  @override
  ConsumerState<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends ConsumerState<WalletScreen> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWallet();
    });
  }

  Future<void> _initializeWallet() async {
    final walletNotifier = ref.read(walletProvider.notifier);
    
    // Check if wallet is initialized
    final walletState = ref.read(walletProvider);
    if (walletState.addresses.isEmpty) {
      // Navigate to wallet setup if no wallet exists
      if (mounted) {
        context.go('/wallet/setup');
      }
      return;
    }

    await _refreshWalletData();
  }

  Future<void> _refreshWalletData() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });

    try {
      final walletNotifier = ref.read(walletProvider.notifier);
      await walletNotifier.refreshBalances();
    } catch (e) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Refresh Failed',
          message: 'Failed to refresh wallet data. Please try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final walletState = ref.watch(walletProvider);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBar(
        title: 'Wallet',
        actions: [
          IconButton(
            icon: Icon(
              Icons.qr_code_scanner,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () => context.push('/wallet/scan'),
            tooltip: 'Scan QR Code',
          ),
          IconButton(
            icon: Icon(
              Icons.settings,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () => context.push('/wallet/settings'),
            tooltip: 'Wallet Settings',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: walletState.isLoading && !_isRefreshing,
        child: RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _refreshWalletData,
          color: theme.colorScheme.primary,
          child: walletState.wallet == null
              ? _buildEmptyState()
              : _buildWalletContent(),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No Wallet Found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create or import a wallet to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () => context.go('/wallet/setup'),
            child: const Text('Setup Wallet'),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletContent() {
    final walletState = ref.watch(walletProvider);
    final wallet = walletState.wallet!;
    
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // Portfolio Overview
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: PortfolioCard(
              portfolioValue: walletState.portfolioValue,
              isLoading: _isRefreshing,
            ),
          ),
        ),

        // Quick Actions
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: QuickActionsRow(
              onSend: () => context.push('/wallet/send'),
              onReceive: () => context.push('/wallet/receive'),
              onBuy: () => context.push('/trading/buy'),
              onSell: () => context.push('/trading/sell'),
            ),
          ),
        ),

        const SliverToBoxAdapter(
          child: SizedBox(height: 24),
        ),

        // Crypto Balances
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Assets',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.push('/wallet/assets'),
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
        ),

        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final balance = wallet.balances[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: CryptoBalanceCard(
                    balance: balance,
                    prices: walletState.prices,
                    onTap: () => context.push('/wallet/asset/${balance.symbol}'),
                  ),
                );
              },
              childCount: wallet.balances.length,
            ),
          ),
        ),

        const SliverToBoxAdapter(
          child: SizedBox(height: 24),
        ),

        // Recent Transactions
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => context.push('/wallet/transactions'),
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
        ),

        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: RecentTransactionsList(
              transactions: wallet.getRecentTransactions(limit: 5),
              onTransactionTap: (transaction) => context.push(
                '/wallet/transaction/${transaction.hash}',
              ),
            ),
          ),
        ),

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }
}

// Wallet Setup Screen for first-time users
class WalletSetupScreen extends ConsumerWidget {
  const WalletSetupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: const CustomAppBar(
        title: 'Setup Wallet',
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 100,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 32),
            Text(
              'Welcome to KryptoPesa Wallet',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Secure, fast, and easy cryptocurrency wallet for P2P trading in East Africa',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: () => context.push('/wallet/create'),
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Text('Create New Wallet'),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => context.push('/wallet/import'),
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Text('Import Existing Wallet'),
                ),
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Your wallet is secured with industry-standard encryption and stored locally on your device.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
