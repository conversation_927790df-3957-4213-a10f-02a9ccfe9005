import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_dialog.dart';
import '../../../core/widgets/success_dialog.dart';
import '../providers/wallet_provider.dart';
import '../models/wallet_models.dart';
import '../widgets/crypto_selector_bottom_sheet.dart';
import '../widgets/address_input_field.dart';
import '../widgets/amount_input_field.dart';
import '../widgets/transaction_fee_card.dart';

class SendTransactionScreen extends ConsumerStatefulWidget {
  final String? initialSymbol;
  final String? initialAddress;
  final String? initialAmount;

  const SendTransactionScreen({
    super.key,
    this.initialSymbol,
    this.initialAddress,
    this.initialAmount,
  });

  @override
  ConsumerState<SendTransactionScreen> createState() => _SendTransactionScreenState();
}

class _SendTransactionScreenState extends ConsumerState<SendTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _addressController = TextEditingController();
  final _amountController = TextEditingController();
  final _memoController = TextEditingController();
  
  CryptoBalance? _selectedCrypto;
  TransactionFeeEstimate? _feeEstimate;
  bool _isEstimatingFee = false;
  bool _isSending = false;
  String? _selectedNetwork;

  @override
  void initState() {
    super.initState();
    
    // Initialize with provided values
    if (widget.initialAddress != null) {
      _addressController.text = widget.initialAddress!;
    }
    if (widget.initialAmount != null) {
      _amountController.text = widget.initialAmount!;
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSelectedCrypto();
    });
  }

  void _initializeSelectedCrypto() {
    final balances = ref.read(walletBalancesProvider);
    if (balances.isNotEmpty) {
      if (widget.initialSymbol != null && balances.containsKey(widget.initialSymbol)) {
        _selectedCrypto = widget.initialSymbol!;
      } else {
        _selectedCrypto = balances.keys.first;
      }
      _selectedNetwork = _getNetworkForCrypto(_selectedCrypto!);
      setState(() {});
    }
  }

  String _getNetworkForCrypto(String crypto) {
    switch (crypto) {
      case 'BTC':
        return 'bitcoin';
      case 'ETH':
      case 'USDT':
      case 'USDC':
        return 'ethereum';
      case 'MATIC':
        return 'polygon';
      default:
        return 'ethereum';
    }
  }

  double _mockEstimateFee(String crypto, double amount) {
    // Mock fee estimation based on crypto type
    switch (crypto) {
      case 'BTC':
        return 0.0001; // ~$4 at current prices
      case 'ETH':
        return 0.002; // ~$8 at current prices
      case 'MATIC':
        return 0.01; // ~$0.01 at current prices
      case 'USDT':
      case 'USDC':
        return 5.0; // $5 for stablecoin transfers
      default:
        return 0.001;
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _amountController.dispose();
    _memoController.dispose();
    super.dispose();
  }

  Future<void> _estimateFee() async {
    if (_selectedCrypto == null || 
        _addressController.text.isEmpty || 
        _amountController.text.isEmpty) {
      return;
    }

    setState(() {
      _isEstimatingFee = true;
      _feeEstimate = null;
    });

    try {
      final request = SendTransactionRequest(
        toAddress: _addressController.text.trim(),
        amount: _amountController.text.trim(),
        symbol: _selectedCrypto!.symbol,
        network: _selectedCrypto!.network,
      );

      // Mock fee estimation
      final estimate = _mockEstimateFee(_selectedCrypto!, amount);
      
      setState(() {
        _feeEstimate = estimate;
        _isEstimatingFee = false;
      });
    } catch (e) {
      setState(() {
        _isEstimatingFee = false;
      });
      
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Fee Estimation Failed',
          message: 'Unable to estimate transaction fee. Please try again.',
        );
      }
    }
  }

  Future<void> _sendTransaction() async {
    if (!_formKey.currentState!.validate() || _selectedCrypto == null) {
      return;
    }

    setState(() {
      _isSending = true;
    });

    try {
      final request = SendTransactionRequest(
        toAddress: _addressController.text.trim(),
        amount: _amountController.text.trim(),
        symbol: _selectedCrypto!.symbol,
        network: _selectedCrypto!.network,
        memo: _memoController.text.trim().isEmpty ? null : _memoController.text.trim(),
      );

      final success = await ref.read(walletProvider.notifier).sendTransaction(
        currency: _selectedCrypto!,
        toAddress: _addressController.text,
        amount: amount,
      );

      setState(() {
        _isSending = false;
      });

      if (success && mounted) {
        await SuccessDialog.show(
          context,
          title: 'Transaction Sent!',
          message: 'Your transaction has been broadcast to the network.\n\nTransaction ID: ${result.txHash}',
        );
        
        context.pop();
      } else if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Transaction Failed',
          message: result.error ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      setState(() {
        _isSending = false;
      });
      
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Transaction Failed',
          message: e.toString(),
        );
      }
    }
  }

  Future<void> _scanQRCode() async {
    try {
      final result = await context.push<String>('/wallet/scan');
      if (result != null && result.isNotEmpty) {
        _addressController.text = result;
        _estimateFee();
      }
    } catch (e) {
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'QR Scan Failed',
          message: 'Unable to scan QR code. Please try again.',
        );
      }
    }
  }

  void _selectCrypto() {
    final balances = ref.read(walletBalancesProvider);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CryptoSelectorBottomSheet(
        balances: balances,
        selectedCrypto: _selectedCrypto,
        onCryptoSelected: (crypto) {
          setState(() {
            _selectedCrypto = crypto;
            _selectedNetwork = crypto.network;
            _feeEstimate = null;
          });
          _estimateFee();
        },
      ),
    );
  }

  void _setMaxAmount() {
    if (_selectedCrypto != null) {
      final maxAmount = _selectedCrypto!.balanceFormatted;
      _amountController.text = maxAmount.toString();
      _estimateFee();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBar(
        title: 'Send Crypto',
        actions: [
          IconButton(
            icon: Icon(Icons.qr_code_scanner),
            onPressed: _scanQRCode,
            tooltip: 'Scan QR Code',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isSending,
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Crypto Selector
                      Text(
                        'Select Cryptocurrency',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildCryptoSelector(theme),
                      
                      const SizedBox(height: 24),
                      
                      // Recipient Address
                      Text(
                        'Recipient Address',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      AddressInputField(
                        controller: _addressController,
                        network: _selectedNetwork ?? 'ethereum',
                        onChanged: (_) => _estimateFee(),
                        onScanPressed: _scanQRCode,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Amount
                      Text(
                        'Amount',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      AmountInputField(
                        controller: _amountController,
                        selectedCrypto: _selectedCrypto,
                        onChanged: (_) => _estimateFee(),
                        onMaxPressed: _setMaxAmount,
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Transaction Fee
                      if (_feeEstimate != null || _isEstimatingFee) ...[
                        Text(
                          'Transaction Fee',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TransactionFeeCard(
                          feeEstimate: _feeEstimate,
                          isLoading: _isEstimatingFee,
                        ),
                        const SizedBox(height: 24),
                      ],
                      
                      // Memo (Optional)
                      Text(
                        'Memo (Optional)',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _memoController,
                        decoration: InputDecoration(
                          hintText: 'Add a note for this transaction',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(Icons.note_add),
                        ),
                        maxLines: 2,
                        maxLength: 100,
                      ),
                    ],
                  ),
                ),
              ),
              
              // Send Button
              Container(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    onPressed: _canSendTransaction() ? _sendTransaction : null,
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isSending
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.onPrimary,
                              ),
                            ),
                          )
                        : Text(
                            'Send Transaction',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCryptoSelector(ThemeData theme) {
    if (_selectedCrypto == null) {
      return Card(
        child: ListTile(
          leading: CircularProgressIndicator(),
          title: Text('Loading cryptocurrencies...'),
        ),
      );
    }

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: ListTile(
        onTap: _selectCrypto,
        leading: _buildCryptoIcon(_selectedCrypto!.symbol),
        title: Text(
          _selectedCrypto!.symbol,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'Balance: ${NumberFormat('#,##0.########').format(_selectedCrypto!.balanceFormatted)}',
        ),
        trailing: Icon(Icons.keyboard_arrow_down),
      ),
    );
  }

  Widget _buildCryptoIcon(String symbol) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getCryptoColor(symbol).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        _getCryptoIcon(symbol),
        color: _getCryptoColor(symbol),
        size: 20,
      ),
    );
  }

  IconData _getCryptoIcon(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.hexagon;
      case 'USDT':
      case 'USDC':
        return Icons.attach_money;
      default:
        return Icons.monetization_on;
    }
  }

  Color _getCryptoColor(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return const Color(0xFFF7931A);
      case 'ETH':
        return const Color(0xFF627EEA);
      case 'MATIC':
        return const Color(0xFF8247E5);
      case 'USDT':
        return const Color(0xFF26A17B);
      case 'USDC':
        return const Color(0xFF2775CA);
      default:
        return const Color(0xFF6B7280);
    }
  }

  bool _canSendTransaction() {
    return _selectedCrypto != null &&
           _addressController.text.isNotEmpty &&
           _amountController.text.isNotEmpty &&
           !_isSending &&
           !_isEstimatingFee;
  }
}
