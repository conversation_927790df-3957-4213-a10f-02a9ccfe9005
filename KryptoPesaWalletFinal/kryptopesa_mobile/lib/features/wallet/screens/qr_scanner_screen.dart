import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/error_dialog.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({super.key});

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool _isFlashOn = false;
  bool _hasPermission = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _checkCameraPermission();
  }

  Future<void> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    if (status.isGranted) {
      setState(() {
        _hasPermission = true;
      });
    } else {
      final result = await Permission.camera.request();
      setState(() {
        _hasPermission = result.isGranted;
      });
    }
  }

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller?.pauseCamera();
    } else if (Platform.isIOS) {
      controller?.resumeCamera();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (!_isProcessing && scanData.code != null) {
        _processQRCode(scanData.code!);
      }
    });
  }

  Future<void> _processQRCode(String qrData) async {
    if (_isProcessing) return;
    
    setState(() {
      _isProcessing = true;
    });

    try {
      // Pause camera to prevent multiple scans
      await controller?.pauseCamera();
      
      // Vibrate to indicate successful scan
      HapticFeedback.mediumImpact();
      
      // Parse QR code data
      final parsedData = _parseQRData(qrData);
      
      if (parsedData != null) {
        // Show success dialog with parsed data
        final shouldProceed = await _showQRDataDialog(parsedData);
        
        if (shouldProceed && mounted) {
          // Return the address to the previous screen
          context.pop(parsedData['address']);
        } else {
          // Resume camera if user cancels
          await controller?.resumeCamera();
          setState(() {
            _isProcessing = false;
          });
        }
      } else {
        // Show error for invalid QR code
        ErrorDialog.show(
          context,
          title: 'Invalid QR Code',
          message: 'This QR code does not contain a valid cryptocurrency address.',
        );
        
        // Resume camera
        await controller?.resumeCamera();
        setState(() {
          _isProcessing = false;
        });
      }
    } catch (e) {
      ErrorDialog.show(
        context,
        title: 'Scan Error',
        message: 'Failed to process QR code: ${e.toString()}',
      );
      
      await controller?.resumeCamera();
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Map<String, String>? _parseQRData(String qrData) {
    try {
      // Handle Bitcoin BIP21 format: bitcoin:address?amount=1.0&label=test
      if (qrData.startsWith('bitcoin:')) {
        final uri = Uri.parse(qrData);
        return {
          'address': uri.path,
          'amount': uri.queryParameters['amount'] ?? '',
          'label': uri.queryParameters['label'] ?? '',
          'network': 'bitcoin',
        };
      }
      
      // Handle Ethereum EIP-681 format: ethereum:address@chainId?value=1.0
      if (qrData.startsWith('ethereum:')) {
        final uri = Uri.parse(qrData);
        final pathParts = uri.path.split('@');
        final address = pathParts[0];
        final chainId = pathParts.length > 1 ? pathParts[1] : '1';
        
        String network = 'ethereum';
        if (chainId == '137') {
          network = 'polygon';
        }
        
        return {
          'address': address,
          'amount': uri.queryParameters['value'] ?? '',
          'network': network,
        };
      }
      
      // Handle plain address (try to detect format)
      if (_isValidAddress(qrData)) {
        return {
          'address': qrData,
          'amount': '',
          'network': _detectNetwork(qrData),
        };
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  bool _isValidAddress(String address) {
    // Bitcoin address patterns
    if (RegExp(r'^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$').hasMatch(address) ||
        RegExp(r'^bc1[a-z0-9]{39,59}$').hasMatch(address) ||
        RegExp(r'^tb1[a-z0-9]{39,59}$').hasMatch(address)) {
      return true;
    }
    
    // Ethereum address pattern
    if (RegExp(r'^0x[a-fA-F0-9]{40}$').hasMatch(address)) {
      return true;
    }
    
    return false;
  }

  String _detectNetwork(String address) {
    // Bitcoin patterns
    if (RegExp(r'^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$').hasMatch(address) ||
        RegExp(r'^bc1[a-z0-9]{39,59}$').hasMatch(address) ||
        RegExp(r'^tb1[a-z0-9]{39,59}$').hasMatch(address)) {
      return 'bitcoin';
    }
    
    // Ethereum/Polygon (default to ethereum)
    if (RegExp(r'^0x[a-fA-F0-9]{40}$').hasMatch(address)) {
      return 'ethereum';
    }
    
    return 'unknown';
  }

  Future<bool> _showQRDataDialog(Map<String, String> data) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('QR Code Scanned'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Address:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(data['address'] ?? '', style: TextStyle(fontFamily: 'monospace')),
            if (data['amount']?.isNotEmpty == true) ...[
              SizedBox(height: 8),
              Text('Amount:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(data['amount']!),
            ],
            if (data['label']?.isNotEmpty == true) ...[
              SizedBox(height: 8),
              Text('Label:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(data['label']!),
            ],
            SizedBox(height: 8),
            Text('Network:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(data['network']?.toUpperCase() ?? 'UNKNOWN'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('Use Address'),
          ),
        ],
      ),
    ) ?? false;
  }

  Future<void> _toggleFlash() async {
    if (controller != null) {
      await controller!.toggleFlash();
      setState(() {
        _isFlashOn = !_isFlashOn;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: CustomAppBar(
        title: 'Scan QR Code',
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isFlashOn ? Icons.flash_on : Icons.flash_off),
            onPressed: _toggleFlash,
            color: Colors.white,
          ),
        ],
      ),
      body: !_hasPermission
          ? _buildPermissionDenied(theme)
          : Column(
              children: [
                Expanded(
                  flex: 4,
                  child: Stack(
                    children: [
                      QRView(
                        key: qrKey,
                        onQRViewCreated: _onQRViewCreated,
                        overlay: QrScannerOverlayShape(
                          borderColor: theme.colorScheme.primary,
                          borderRadius: 16,
                          borderLength: 30,
                          borderWidth: 8,
                          cutOutSize: 250,
                        ),
                      ),
                      if (_isProcessing)
                        Container(
                          color: Colors.black54,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    theme.colorScheme.primary,
                                  ),
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Processing QR Code...',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Container(
                    color: Colors.black,
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.qr_code_scanner,
                              color: Colors.white,
                              size: 32,
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Point your camera at a QR code',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Supports Bitcoin and Ethereum addresses',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildPermissionDenied(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: 80,
              color: Colors.white70,
            ),
            SizedBox(height: 24),
            Text(
              'Camera Permission Required',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'To scan QR codes, please allow camera access in your device settings.',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32),
            FilledButton(
              onPressed: () {
                openAppSettings();
              },
              child: Text('Open Settings'),
            ),
          ],
        ),
      ),
    );
  }
}
