import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/wallet_models.dart';

class CryptoBalanceCard extends StatelessWidget {
  final CryptoBalance balance;
  final CryptoPrices? prices;
  final VoidCallback? onTap;

  const CryptoBalanceCard({
    super.key,
    required this.balance,
    this.prices,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
    final cryptoFormat = NumberFormat('#,##0.########');

    final price = prices?.getPrice(balance.symbol, 'USD') ?? balance.priceUSD ?? 0.0;
    final change24h = prices?.getChange24h(balance.symbol, 'USD') ?? balance.change24h ?? 0.0;
    final valueUSD = balance.balanceFormatted * price;

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Crypto Icon
              _buildCryptoIcon(theme),
              const SizedBox(width: 12),
              
              // Crypto Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          balance.displaySymbol,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildNetworkBadge(theme),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getFullName(balance.symbol),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Balance & Value
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    cryptoFormat.format(balance.balanceFormatted),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        currencyFormat.format(valueUSD),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildPriceChange(theme, change24h),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCryptoIcon(ThemeData theme) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getCryptoColor(balance.symbol).withOpacity(0.1),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Icon(
        _getCryptoIcon(balance.symbol),
        color: _getCryptoColor(balance.symbol),
        size: 24,
      ),
    );
  }

  Widget _buildNetworkBadge(ThemeData theme) {
    if (balance.symbol == 'BTC') return const SizedBox.shrink();

    Color badgeColor;
    String networkName;

    switch (balance.network) {
      case 'polygon':
        badgeColor = const Color(0xFF8247E5);
        networkName = 'Polygon';
        break;
      case 'ethereum':
        badgeColor = const Color(0xFF627EEA);
        networkName = 'Ethereum';
        break;
      default:
        badgeColor = theme.colorScheme.outline;
        networkName = balance.network.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: badgeColor.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        networkName,
        style: theme.textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPriceChange(ThemeData theme, double change24h) {
    final isPositive = change24h >= 0;
    final color = isPositive ? Colors.green : Colors.red;
    final icon = isPositive ? Icons.trending_up : Icons.trending_down;
    final percentFormat = NumberFormat('+#,##0.00%;-#,##0.00%');

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 2),
          Text(
            percentFormat.format(change24h / 100),
            style: theme.textTheme.labelSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCryptoIcon(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.polygon;
      case 'USDT':
      case 'USDC':
        return Icons.attach_money;
      case 'DAI':
        return Icons.account_balance;
      default:
        return Icons.monetization_on;
    }
  }

  Color _getCryptoColor(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return const Color(0xFFF7931A);
      case 'ETH':
        return const Color(0xFF627EEA);
      case 'MATIC':
        return const Color(0xFF8247E5);
      case 'USDT':
        return const Color(0xFF26A17B);
      case 'USDC':
        return const Color(0xFF2775CA);
      case 'DAI':
        return const Color(0xFFF5AC37);
      default:
        return const Color(0xFF6B7280);
    }
  }

  String _getFullName(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return 'Bitcoin';
      case 'ETH':
        return 'Ethereum';
      case 'MATIC':
        return 'Polygon';
      case 'USDT':
        return 'Tether USD';
      case 'USDC':
        return 'USD Coin';
      case 'DAI':
        return 'Dai Stablecoin';
      default:
        return symbol.toUpperCase();
    }
  }
}
