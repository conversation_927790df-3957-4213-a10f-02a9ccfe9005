import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AddressInput<PERSON>ield extends StatelessWidget {
  final TextEditingController controller;
  final String network;
  final Function(String)? onChanged;
  final VoidCallback? onScanPressed;

  const AddressInputField({
    super.key,
    required this.controller,
    required this.network,
    this.onChanged,
    this.onScanPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        hintText: _getHintText(),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: Icon(_getNetworkIcon()),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: _pasteFromClipboard,
              icon: Icon(Icons.paste),
              tooltip: 'Paste from clipboard',
            ),
            IconButton(
              onPressed: onScanPressed,
              icon: Icon(Icons.qr_code_scanner),
              tooltip: 'Scan QR code',
            ),
          ],
        ),
        helperText: _getHelperText(),
        helperMaxLines: 2,
      ),
      validator: _validateAddress,
      onChanged: onChanged,
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.next,
    );
  }

  String _getHintText() {
    switch (network) {
      case 'bitcoin':
        return 'Enter Bitcoin address (bc1... or 1... or 3...)';
      case 'ethereum':
        return 'Enter Ethereum address (0x...)';
      case 'polygon':
        return 'Enter Polygon address (0x...)';
      default:
        return 'Enter cryptocurrency address';
    }
  }

  String _getHelperText() {
    switch (network) {
      case 'bitcoin':
        return 'Bitcoin addresses start with 1, 3, or bc1';
      case 'ethereum':
        return 'Ethereum addresses start with 0x and are 42 characters long';
      case 'polygon':
        return 'Polygon addresses start with 0x and are 42 characters long';
      default:
        return 'Make sure the address is correct for the selected network';
    }
  }

  IconData _getNetworkIcon() {
    switch (network) {
      case 'bitcoin':
        return Icons.currency_bitcoin;
      case 'ethereum':
        return Icons.diamond;
      case 'polygon':
        return Icons.polygon;
      default:
        return Icons.account_balance_wallet;
    }
  }

  String? _validateAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter a recipient address';
    }

    final address = value.trim();

    switch (network) {
      case 'bitcoin':
        return _validateBitcoinAddress(address);
      case 'ethereum':
      case 'polygon':
        return _validateEthereumAddress(address);
      default:
        return null;
    }
  }

  String? _validateBitcoinAddress(String address) {
    // Legacy P2PKH addresses (start with 1)
    if (RegExp(r'^1[a-km-zA-HJ-NP-Z1-9]{25,34}$').hasMatch(address)) {
      return null;
    }
    
    // Legacy P2SH addresses (start with 3)
    if (RegExp(r'^3[a-km-zA-HJ-NP-Z1-9]{25,34}$').hasMatch(address)) {
      return null;
    }
    
    // Bech32 addresses (start with bc1 for mainnet, tb1 for testnet)
    if (RegExp(r'^(bc1|tb1)[a-z0-9]{39,59}$').hasMatch(address)) {
      return null;
    }
    
    return 'Invalid Bitcoin address format';
  }

  String? _validateEthereumAddress(String address) {
    // Ethereum addresses are 42 characters long and start with 0x
    if (!RegExp(r'^0x[a-fA-F0-9]{40}$').hasMatch(address)) {
      return 'Invalid Ethereum address format';
    }
    
    return null;
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        controller.text = clipboardData!.text!.trim();
        onChanged?.call(controller.text);
      }
    } catch (e) {
      // Handle clipboard access error silently
    }
  }
}
