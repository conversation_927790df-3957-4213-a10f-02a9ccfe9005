import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Wallet State
class WalletState {
  final bool isLoading;
  final String? error;
  final List<dynamic> wallets;
  final List<dynamic> transactions;
  final double totalBalance;

  const WalletState({
    this.isLoading = false,
    this.error,
    this.wallets = const [],
    this.transactions = const [],
    this.totalBalance = 0.0,
  });

  WalletState copyWith({
    bool? isLoading,
    String? error,
    List<dynamic>? wallets,
    List<dynamic>? transactions,
    double? totalBalance,
  }) {
    return WalletState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      wallets: wallets ?? this.wallets,
      transactions: transactions ?? this.transactions,
      totalBalance: totalBalance ?? this.totalBalance,
    );
  }
}

/// Wallet Notifier
class WalletNotifier extends StateNotifier<WalletState> {
  WalletNotifier() : super(const WalletState());

  Future<void> loadWallets() async {
    // TODO: Implement wallet loading
    state = state.copyWith(isLoading: true);
    // Simulate loading
    await Future.delayed(const Duration(seconds: 1));
    state = state.copyWith(isLoading: false);
  }
}

/// Wallet Provider
final walletProvider = StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  return WalletNotifier();
});
