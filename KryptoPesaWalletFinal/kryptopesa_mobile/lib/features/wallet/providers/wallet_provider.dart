import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/logger.dart';

// Wallet State
class WalletState {
  final Map<String, double> balances;
  final List<String> supportedCurrencies;
  final String? selectedCurrency;
  final bool isLoading;
  final String? error;
  final Map<String, String> addresses;
  final List<Transaction> transactions;

  const WalletState({
    this.balances = const {},
    this.supportedCurrencies = const ['BTC', 'ETH', 'MATIC', 'USDT', 'USDC'],
    this.selectedCurrency,
    this.isLoading = false,
    this.error,
    this.addresses = const {},
    this.transactions = const [],
  });

  WalletState copyWith({
    Map<String, double>? balances,
    List<String>? supportedCurrencies,
    String? selectedCurrency,
    bool? isLoading,
    String? error,
    Map<String, String>? addresses,
    List<Transaction>? transactions,
  }) {
    return WalletState(
      balances: balances ?? this.balances,
      supportedCurrencies: supportedCurrencies ?? this.supportedCurrencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      addresses: addresses ?? this.addresses,
      transactions: transactions ?? this.transactions,
    );
  }

  double getBalance(String currency) => balances[currency] ?? 0.0;
  String? getAddress(String currency) => addresses[currency];
  bool get hasError => error != null;
}

// Transaction Model
class Transaction {
  final String id;
  final String currency;
  final double amount;
  final String type; // 'send' or 'receive'
  final String? toAddress;
  final String? fromAddress;
  final DateTime timestamp;
  final String status;
  final String? txHash;

  const Transaction({
    required this.id,
    required this.currency,
    required this.amount,
    required this.type,
    this.toAddress,
    this.fromAddress,
    required this.timestamp,
    this.status = 'pending',
    this.txHash,
  });
}

// Wallet Notifier
class WalletNotifier extends StateNotifier<WalletState> {
  WalletNotifier() : super(const WalletState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true);
      
      // Initialize wallet addresses
      final addresses = <String, String>{};
      for (final currency in state.supportedCurrencies) {
        addresses[currency] = _generateAddress(currency);
      }
      
      // Load balances (mock data for now)
      final balances = <String, double>{
        'BTC': 0.05432,
        'ETH': 1.2345,
        'MATIC': 150.0,
        'USDT': 500.0,
        'USDC': 250.0,
      };

      state = state.copyWith(
        addresses: addresses,
        balances: balances,
        selectedCurrency: 'BTC',
        isLoading: false,
      );

      AppLogger.info('Wallet initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize wallet', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  String _generateAddress(String currency) {
    // Mock address generation
    switch (currency) {
      case 'BTC':
        return '**********************************';
      case 'ETH':
        return '******************************************';
      case 'MATIC':
        return '******************************************';
      case 'USDT':
        return '******************************************';
      case 'USDC':
        return '******************************************';
      default:
        return '******************************************';
    }
  }

  void selectCurrency(String currency) {
    if (state.supportedCurrencies.contains(currency)) {
      state = state.copyWith(selectedCurrency: currency);
      AppLogger.info('Selected currency: $currency');
    }
  }

  Future<void> refreshBalances() async {
    try {
      state = state.copyWith(isLoading: true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock updated balances
      final updatedBalances = Map<String, double>.from(state.balances);
      for (final currency in updatedBalances.keys) {
        updatedBalances[currency] = updatedBalances[currency]! * (0.95 + (0.1 * (DateTime.now().millisecondsSinceEpoch % 100) / 100));
      }

      state = state.copyWith(
        balances: updatedBalances,
        isLoading: false,
      );

      AppLogger.info('Balances refreshed');
    } catch (e) {
      AppLogger.error('Failed to refresh balances', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> sendTransaction({
    required String currency,
    required String toAddress,
    required double amount,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Validate balance
      final currentBalance = state.getBalance(currency);
      if (amount > currentBalance) {
        throw Exception('Insufficient balance');
      }

      // Simulate transaction
      await Future.delayed(const Duration(seconds: 2));

      // Update balance
      final updatedBalances = Map<String, double>.from(state.balances);
      updatedBalances[currency] = currentBalance - amount;

      // Add transaction to history
      final transaction = Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        currency: currency,
        amount: amount,
        type: 'send',
        toAddress: toAddress,
        timestamp: DateTime.now(),
        status: 'completed',
        txHash: '0x${DateTime.now().millisecondsSinceEpoch.toRadixString(16)}',
      );

      final updatedTransactions = [transaction, ...state.transactions];

      state = state.copyWith(
        balances: updatedBalances,
        transactions: updatedTransactions,
        isLoading: false,
      );

      AppLogger.info('Transaction sent successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to send transaction', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final walletProvider = StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  return WalletNotifier();
});

final walletBalancesProvider = Provider<Map<String, double>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.balances;
});

final selectedCurrencyProvider = Provider<String?>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.selectedCurrency;
});

final walletAddressesProvider = Provider<Map<String, String>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.addresses;
});

final transactionHistoryProvider = Provider<List<Transaction>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.transactions;
});
