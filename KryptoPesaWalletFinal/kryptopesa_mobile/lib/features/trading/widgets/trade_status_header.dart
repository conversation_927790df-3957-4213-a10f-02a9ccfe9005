import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/trading_models.dart';

class TradeStatusHeader extends StatelessWidget {
  final Trade trade;

  const TradeStatusHeader({
    super.key,
    required this.trade,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '', decimalDigits: 2);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: _getStatusColor(trade.status).withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Status badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(trade.status),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              _getStatusText(trade.status),
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Trade details
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  context,
                  'Amount',
                  '${currencyFormat.format(trade.amount)} ${trade.fiatCurrency}',
                  Icons.attach_money,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
              Expanded(
                child: _buildDetailItem(
                  context,
                  'Crypto',
                  '${trade.displayCryptoAmount}',
                  _getCryptoIcon(trade.cryptocurrency),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Payment method
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment,
                  size: 16,
                  color: theme.colorScheme.outline,
                ),
                const SizedBox(width: 8),
                Text(
                  trade.paymentMethod,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          // Action buttons based on status
          if (_shouldShowActionButtons(trade.status)) ...[
            const SizedBox(height: 16),
            _buildActionButtons(context, theme),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    switch (trade.status) {
      case TradeStatus.accepted:
        return _buildFundEscrowButton(context, theme);
      case TradeStatus.escrowed:
        return _buildPaymentButtons(context, theme);
      case TradeStatus.paid:
        return _buildConfirmPaymentButton(context, theme);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildFundEscrowButton(BuildContext context, ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: () {
          // Handle fund escrow
          _showFundEscrowDialog(context);
        },
        icon: Icon(Icons.lock),
        label: Text('Fund Escrow'),
        style: FilledButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentButtons(BuildContext context, ThemeData theme) {
    if (trade.offer.type == OfferType.buy) {
      // Buyer should send payment
      return SizedBox(
        width: double.infinity,
        child: FilledButton.icon(
          onPressed: () {
            // Handle mark payment sent
            _showMarkPaymentSentDialog(context);
          },
          icon: Icon(Icons.send),
          label: Text('Mark Payment Sent'),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    } else {
      // Seller waits for payment
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.blue.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.hourglass_empty,
              color: Colors.blue,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Waiting for buyer to send payment',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildConfirmPaymentButton(BuildContext context, ThemeData theme) {
    if (trade.offer.type == OfferType.sell) {
      // Seller should confirm payment received
      return SizedBox(
        width: double.infinity,
        child: FilledButton.icon(
          onPressed: () {
            // Handle confirm payment
            _showConfirmPaymentDialog(context);
          },
          icon: Icon(Icons.check_circle),
          label: Text('Confirm Payment Received'),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    } else {
      // Buyer waits for confirmation
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.green.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.hourglass_empty,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Waiting for seller to confirm payment',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  void _showFundEscrowDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Fund Escrow'),
        content: Text(
          'This will transfer ${trade.displayCryptoAmount} to the escrow smart contract. '
          'The funds will be held securely until the trade is completed.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement fund escrow
            },
            child: Text('Fund Escrow'),
          ),
        ],
      ),
    );
  }

  void _showMarkPaymentSentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Mark Payment Sent'),
        content: Text(
          'Have you sent ${trade.displayAmount} via ${trade.paymentMethod}? '
          'Only mark this as sent after you have actually sent the payment.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement mark payment sent
            },
            child: Text('Mark as Sent'),
          ),
        ],
      ),
    );
  }

  void _showConfirmPaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm Payment'),
        content: Text(
          'Have you received ${trade.displayAmount} via ${trade.paymentMethod}? '
          'This will release the crypto from escrow to the buyer.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement confirm payment
            },
            child: Text('Confirm Received'),
          ),
        ],
      ),
    );
  }

  bool _shouldShowActionButtons(TradeStatus status) {
    return [
      TradeStatus.accepted,
      TradeStatus.escrowed,
      TradeStatus.paid,
    ].contains(status);
  }

  Color _getStatusColor(TradeStatus status) {
    switch (status) {
      case TradeStatus.pending:
        return Colors.orange;
      case TradeStatus.accepted:
        return Colors.blue;
      case TradeStatus.escrowed:
        return Colors.purple;
      case TradeStatus.paid:
        return Colors.indigo;
      case TradeStatus.completed:
        return Colors.green;
      case TradeStatus.cancelled:
        return Colors.red;
      case TradeStatus.disputed:
        return Colors.red;
    }
  }

  String _getStatusText(TradeStatus status) {
    switch (status) {
      case TradeStatus.pending:
        return 'PENDING';
      case TradeStatus.accepted:
        return 'ACCEPTED';
      case TradeStatus.escrowed:
        return 'ESCROWED';
      case TradeStatus.paid:
        return 'PAYMENT SENT';
      case TradeStatus.completed:
        return 'COMPLETED';
      case TradeStatus.cancelled:
        return 'CANCELLED';
      case TradeStatus.disputed:
        return 'DISPUTED';
    }
  }

  IconData _getCryptoIcon(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.polygon;
      case 'USDT':
      case 'USDC':
        return Icons.attach_money;
      case 'DAI':
        return Icons.account_balance;
      default:
        return Icons.monetization_on;
    }
  }
}
