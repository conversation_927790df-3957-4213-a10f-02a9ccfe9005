import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Trading State
class TradingState {
  final bool isLoading;
  final String? error;
  final List<dynamic> offers;
  final List<dynamic> trades;

  const TradingState({
    this.isLoading = false,
    this.error,
    this.offers = const [],
    this.trades = const [],
  });

  TradingState copyWith({
    bool? isLoading,
    String? error,
    List<dynamic>? offers,
    List<dynamic>? trades,
  }) {
    return TradingState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      offers: offers ?? this.offers,
      trades: trades ?? this.trades,
    );
  }
}

/// Trading Notifier
class TradingNotifier extends StateNotifier<TradingState> {
  TradingNotifier() : super(const TradingState());

  Future<void> loadOffers() async {
    // TODO: Implement offers loading
    state = state.copyWith(isLoading: true);
    await Future.delayed(const Duration(seconds: 1));
    state = state.copyWith(isLoading: false);
  }
}

/// Trading Provider
final tradingProvider = StateNotifierProvider<TradingNotifier, TradingState>((ref) {
  return TradingNotifier();
});
