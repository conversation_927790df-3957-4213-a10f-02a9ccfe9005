import 'dart:async';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../utils/logger.dart';
import 'local_database_service.dart';
import 'connectivity_service.dart';
import 'offline_queue_service.dart';
import '../network/api_client.dart';

enum SyncStatus {
  idle,
  syncing,
  completed,
  failed,
  conflict,
}

enum ConflictResolution {
  serverWins,
  clientWins,
  merge,
  manual,
}

class SyncResult {
  final SyncStatus status;
  final int itemsSynced;
  final int conflicts;
  final List<String> errors;
  final DateTime timestamp;

  const SyncResult({
    required this.status,
    this.itemsSynced = 0,
    this.conflicts = 0,
    this.errors = const [],
    required this.timestamp,
  });

  bool get isSuccess => status == SyncStatus.completed;
  bool get hasConflicts => conflicts > 0;
  bool get hasErrors => errors.isNotEmpty;
}

class SyncMetadata {
  final String entityType;
  final String entityId;
  final DateTime lastSyncTimestamp;
  final String syncHash;
  final ConflictResolution conflictResolution;

  const SyncMetadata({
    required this.entityType,
    required this.entityId,
    required this.lastSyncTimestamp,
    required this.syncHash,
    this.conflictResolution = ConflictResolution.serverWins,
  });

  Map<String, dynamic> toMap() {
    return {
      'entity_type': entityType,
      'entity_id': entityId,
      'last_sync_timestamp': lastSyncTimestamp.millisecondsSinceEpoch,
      'sync_hash': syncHash,
      'conflict_resolution': conflictResolution.name,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

class SyncService {
  static SyncService? _instance;
  static SyncService get instance => _instance ??= SyncService._();
  
  SyncService._();

  final LocalDatabaseService _database = LocalDatabaseService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineQueueService _queue = OfflineQueueService.instance;
  final ApiClient _apiClient = ApiClient.instance;

  final StreamController<SyncResult> _syncResultController = 
      StreamController<SyncResult>.broadcast();

  Timer? _autoSyncTimer;
  bool _isSyncing = false;
  SyncStatus _currentStatus = SyncStatus.idle;

  static const Duration _autoSyncInterval = Duration(minutes: 5);
  static const Duration _backgroundSyncInterval = Duration(minutes: 15);

  /// Initialize sync service
  Future<void> initialize() async {
    try {
      AppLogger.info('Initializing sync service...');

      // Start auto-sync timer
      _startAutoSync();

      // Listen to connectivity changes for opportunistic sync
      _connectivity.statusStream.listen((status) {
        if (status.isConnected && status.quality.index >= ConnectionQuality.fair.index) {
          _performOpportunisticSync();
        }
      });

      AppLogger.info('Sync service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize sync service', e);
    }
  }

  /// Stream of sync results
  Stream<SyncResult> get syncResultStream => _syncResultController.stream;

  /// Current sync status
  SyncStatus get currentStatus => _currentStatus;

  /// Check if currently syncing
  bool get isSyncing => _isSyncing;

  /// Perform full sync
  Future<SyncResult> performFullSync() async {
    if (_isSyncing) {
      AppLogger.warning('Sync already in progress');
      return SyncResult(
        status: SyncStatus.failed,
        errors: ['Sync already in progress'],
        timestamp: DateTime.now(),
      );
    }

    if (!_connectivity.isConnected) {
      AppLogger.warning('No internet connection for sync');
      return SyncResult(
        status: SyncStatus.failed,
        errors: ['No internet connection'],
        timestamp: DateTime.now(),
      );
    }

    _isSyncing = true;
    _currentStatus = SyncStatus.syncing;

    try {
      AppLogger.info('Starting full sync...');

      int totalSynced = 0;
      int totalConflicts = 0;
      List<String> allErrors = [];

      // Sync user data
      final userResult = await _syncUserData();
      totalSynced += userResult.itemsSynced;
      totalConflicts += userResult.conflicts;
      allErrors.addAll(userResult.errors);

      // Sync wallet data
      final walletResult = await _syncWalletData();
      totalSynced += walletResult.itemsSynced;
      totalConflicts += walletResult.conflicts;
      allErrors.addAll(walletResult.errors);

      // Sync trading data
      final tradingResult = await _syncTradingData();
      totalSynced += tradingResult.itemsSynced;
      totalConflicts += tradingResult.conflicts;
      allErrors.addAll(tradingResult.errors);

      // Sync messages
      final messageResult = await _syncMessages();
      totalSynced += messageResult.itemsSynced;
      totalConflicts += messageResult.conflicts;
      allErrors.addAll(messageResult.errors);

      final finalStatus = allErrors.isEmpty 
          ? SyncStatus.completed 
          : SyncStatus.failed;

      final result = SyncResult(
        status: finalStatus,
        itemsSynced: totalSynced,
        conflicts: totalConflicts,
        errors: allErrors,
        timestamp: DateTime.now(),
      );

      _currentStatus = finalStatus;
      _syncResultController.add(result);

      AppLogger.info(
        'Full sync completed: ${result.itemsSynced} items, '
        '${result.conflicts} conflicts, ${result.errors.length} errors'
      );

      return result;

    } catch (e) {
      AppLogger.error('Full sync failed', e);
      
      final result = SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );

      _currentStatus = SyncStatus.failed;
      _syncResultController.add(result);

      return result;
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync user data
  Future<SyncResult> _syncUserData() async {
    try {
      AppLogger.debug('Syncing user data...');

      // Get local user data that needs syncing
      // This would integrate with your user data storage
      
      // For now, return empty result
      return SyncResult(
        status: SyncStatus.completed,
        itemsSynced: 0,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('Failed to sync user data', e);
      return SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Sync wallet data
  Future<SyncResult> _syncWalletData() async {
    try {
      AppLogger.debug('Syncing wallet data...');

      // Get local wallet data
      // Sync with server
      // Handle conflicts
      
      // For now, return empty result
      return SyncResult(
        status: SyncStatus.completed,
        itemsSynced: 0,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('Failed to sync wallet data', e);
      return SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Sync trading data
  Future<SyncResult> _syncTradingData() async {
    try {
      AppLogger.debug('Syncing trading data...');

      // Get local trading data
      // Sync with server
      // Handle conflicts
      
      // For now, return empty result
      return SyncResult(
        status: SyncStatus.completed,
        itemsSynced: 0,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('Failed to sync trading data', e);
      return SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Sync messages
  Future<SyncResult> _syncMessages() async {
    try {
      AppLogger.debug('Syncing messages...');

      // Get local messages that need syncing
      // Sync with server
      // Handle conflicts
      
      // For now, return empty result
      return SyncResult(
        status: SyncStatus.completed,
        itemsSynced: 0,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('Failed to sync messages', e);
      return SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Calculate data hash for conflict detection
  String _calculateDataHash(Map<String, dynamic> data) {
    final jsonString = json.encode(data);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Store sync metadata
  Future<void> _storeSyncMetadata(SyncMetadata metadata) async {
    try {
      final db = await _database.database;
      await db.insert(
        'sync_metadata',
        metadata.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      AppLogger.error('Failed to store sync metadata', e);
    }
  }

  /// Get sync metadata
  Future<SyncMetadata?> _getSyncMetadata(String entityType, String entityId) async {
    try {
      final db = await _database.database;
      final result = await db.query(
        'sync_metadata',
        where: 'entity_type = ? AND entity_id = ?',
        whereArgs: [entityType, entityId],
      );

      if (result.isNotEmpty) {
        final row = result.first;
        return SyncMetadata(
          entityType: row['entity_type'] as String,
          entityId: row['entity_id'] as String,
          lastSyncTimestamp: DateTime.fromMillisecondsSinceEpoch(
            row['last_sync_timestamp'] as int
          ),
          syncHash: row['sync_hash'] as String,
          conflictResolution: ConflictResolution.values.firstWhere(
            (e) => e.name == row['conflict_resolution'],
            orElse: () => ConflictResolution.serverWins,
          ),
        );
      }

      return null;
    } catch (e) {
      AppLogger.error('Failed to get sync metadata', e);
      return null;
    }
  }

  /// Perform opportunistic sync when connection is restored
  Future<void> _performOpportunisticSync() async {
    if (_isSyncing) return;

    AppLogger.info('Performing opportunistic sync...');
    
    // Wait a bit to avoid immediate sync on connection restore
    await Future.delayed(const Duration(seconds: 5));
    
    if (_connectivity.isConnected) {
      performFullSync();
    }
  }

  /// Start auto-sync timer
  void _startAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = Timer.periodic(_autoSyncInterval, (timer) {
      if (_connectivity.isConnected && 
          _connectivity.isGoodConnection && 
          !_isSyncing) {
        performFullSync();
      }
    });
  }

  /// Sync specific entity
  Future<SyncResult> syncEntity(String entityType, String entityId) async {
    try {
      AppLogger.info('Syncing entity: $entityType/$entityId');

      // Implementation would depend on entity type
      switch (entityType) {
        case 'user':
          return await _syncUserData();
        case 'wallet':
          return await _syncWalletData();
        case 'trade':
          return await _syncTradingData();
        case 'message':
          return await _syncMessages();
        default:
          throw UnsupportedError('Entity type $entityType not supported');
      }
    } catch (e) {
      AppLogger.error('Failed to sync entity $entityType/$entityId', e);
      return SyncResult(
        status: SyncStatus.failed,
        errors: [e.toString()],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Force immediate sync
  Future<SyncResult> forceSync() async {
    AppLogger.info('Force sync requested...');
    return await performFullSync();
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return {
      'current_status': _currentStatus.name,
      'is_syncing': _isSyncing,
      'is_connected': _connectivity.isConnected,
      'connection_quality': _connectivity.currentStatus.quality.name,
      'auto_sync_enabled': _autoSyncTimer?.isActive ?? false,
    };
  }

  /// Enable/disable auto-sync
  void setAutoSyncEnabled(bool enabled) {
    if (enabled) {
      _startAutoSync();
      AppLogger.info('Auto-sync enabled');
    } else {
      _autoSyncTimer?.cancel();
      AppLogger.info('Auto-sync disabled');
    }
  }

  /// Dispose resources
  void dispose() {
    _autoSyncTimer?.cancel();
    _syncResultController.close();
    
    AppLogger.info('Sync service disposed');
  }
}
