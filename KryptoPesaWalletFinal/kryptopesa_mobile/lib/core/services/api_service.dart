import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../constants/api_constants.dart';

class ApiService {
  static late Dio _dio;
  static String? _authToken;
  static bool _isInitialized = false;

  // Initialize the API service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => AppLogger.debug('HTTP: ${obj.toString()}'),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        AppLogger.info('API Response: ${response.requestOptions.path} - ${response.statusCode}');
        handler.next(response);
      },
      onError: (error, handler) async {
        AppLogger.error('API Error: ${error.requestOptions.path}', error);

        // Handle 401 unauthorized
        if (error.response?.statusCode == 401) {
          await _handleUnauthorized();
        }

        handler.next(error);
      },
    ));

    // Load saved auth token
    await _loadAuthToken();
    _isInitialized = true;
  }

  // Load auth token from storage
  static Future<void> _loadAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _authToken = prefs.getString('auth_token');
      AppLogger.debug('Auth token loaded: ${_authToken != null}');
    } catch (e) {
      AppLogger.error('Failed to load auth token', e);
    }
  }

  // Save auth token to storage
  static Future<void> _saveAuthToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      _authToken = token;
      AppLogger.debug('Auth token saved');
    } catch (e) {
      AppLogger.error('Failed to save auth token', e);
    }
  }

  // Clear auth token
  static Future<void> _clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      _authToken = null;
      AppLogger.debug('Auth token cleared');
    } catch (e) {
      AppLogger.error('Failed to clear auth token', e);
    }
  }

  // Handle unauthorized response
  static Future<void> _handleUnauthorized() async {
    await _clearAuthToken();
    // Navigate to login screen
    // This will be handled by the app's navigation system
  }

  // Set auth token
  static Future<void> setAuthToken(String token) async {
    await _saveAuthToken(token);
  }

  // Clear auth token
  static Future<void> clearAuthToken() async {
    await _clearAuthToken();
  }

  // Check if user is authenticated
  static bool get isAuthenticated => _authToken != null;

  // Get auth token
  static String? get authToken => _authToken;

  // Generic GET request
  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      return ApiResponse.fromResponse(response, fromJson);
    } on DioException catch (e) {
      return ApiResponse.fromError(e);
    }
  }

  // Generic POST request
  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      return ApiResponse.fromResponse(response, fromJson);
    } on DioException catch (e) {
      return ApiResponse.fromError(e);
    }
  }

  // Generic PUT request
  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      return ApiResponse.fromResponse(response, fromJson);
    } on DioException catch (e) {
      return ApiResponse.fromError(e);
    }
  }

  // Generic DELETE request
  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
      );
      return ApiResponse.fromResponse(response, fromJson);
    } on DioException catch (e) {
      return ApiResponse.fromError(e);
    }
  }

  // Upload file
  static Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    String filePath, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(filePath),
        ...?additionalData,
      });

      final response = await _dio.post(
        endpoint,
        data: formData,
      );
      return ApiResponse.fromResponse(response, fromJson);
    } on DioException catch (e) {
      return ApiResponse.fromError(e);
    }
  }
}

// API Response wrapper
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? error;
  final int? statusCode;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.statusCode,
  });

  factory ApiResponse.fromResponse(
    Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    final responseData = response.data as Map<String, dynamic>;
    
    return ApiResponse<T>(
      success: responseData['success'] ?? false,
      message: responseData['message'],
      data: fromJson != null && responseData['data'] != null
          ? fromJson(responseData['data'])
          : responseData['data'],
      statusCode: response.statusCode,
    );
  }

  factory ApiResponse.fromError(DioException error) {
    final responseData = error.response?.data as Map<String, dynamic>?;
    
    return ApiResponse<T>(
      success: false,
      message: responseData?['message'] ?? error.message,
      error: responseData?['error'] ?? {'message': error.message},
      statusCode: error.response?.statusCode,
    );
  }
}
