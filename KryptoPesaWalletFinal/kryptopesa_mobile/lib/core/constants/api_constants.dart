class ApiConstants {
  // Base URLs
  // Use 192.168.1.3 for physical device testing, 10.0.2.2 for emulator
  static const String baseUrl = 'http://192.168.1.3:3000/api';
  static const String websocketUrl = 'http://192.168.1.3:3000';
  
  // Production URLs (for future use)
  static const String prodBaseUrl = 'https://api.kryptopesa.com/api';
  static const String prodWebsocketUrl = 'https://api.kryptopesa.com';

  // Authentication endpoints
  static const String authRegister = '/auth/register';
  static const String authLogin = '/auth/login';
  static const String authLogout = '/auth/logout';
  static const String authMe = '/auth/me';
  static const String authRefresh = '/auth/refresh';
  static const String authForgotPassword = '/auth/forgot-password';
  static const String authResetPassword = '/auth/reset-password';
  static const String authVerifyEmail = '/auth/verify-email';
  static const String authResendVerification = '/auth/resend-verification';

  // User endpoints
  static const String userProfile = '/users/profile';
  static const String userUpdate = '/users/profile';
  static const String userAvatar = '/users/avatar';
  static const String userReputation = '/users/{userId}/reputation';
  static const String userDashboard = '/users/dashboard';
  static const String userSettings = '/users/settings';
  static const String userNotifications = '/users/notifications';

  // Wallet endpoints
  static const String walletCreate = '/wallet/create';
  static const String walletImport = '/wallet/import';
  static const String walletGet = '/wallet';
  static const String walletBalances = '/wallet/balances/refresh';
  static const String walletTransactions = '/wallet/transactions';
  static const String walletSend = '/wallet/send';
  static const String walletReceive = '/wallet/receive';
  static const String walletHistory = '/wallet/history';
  static const String walletEstimateFee = '/wallet/estimate-fee';
  static const String walletPrices = '/wallet/prices';
  static const String walletPortfolioValue = '/wallet/portfolio-value';
  static const String walletVerifyMnemonic = '/wallet/verify-mnemonic';
  static const String walletBackupComplete = '/wallet/backup/complete';
  static const String walletStats = '/wallet/stats';

  // Offer endpoints
  static const String offers = '/offers';
  static const String offerCreate = '/offers';
  static const String offerUpdate = '/offers/{offerId}';
  static const String offerDelete = '/offers/{offerId}';
  static const String offerSearch = '/offers/search';
  static const String offerMy = '/offers/my';

  // Trade endpoints
  static const String trades = '/trades';
  static const String tradeCreate = '/trades';
  static const String tradeUpdate = '/trades/{tradeId}';
  static const String tradeHistory = '/trades/history';
  static const String tradeActive = '/trades/active';
  static const String tradeConfirm = '/trades/{tradeId}/confirm';
  static const String tradeCancel = '/trades/{tradeId}/cancel';
  static const String tradeDispute = '/trades/{tradeId}/dispute';
  static const String tradeUploadProof = '/trades/{tradeId}/proof';

  // Chat endpoints
  static const String chatMessages = '/chat/{tradeId}/messages';
  static const String chatSend = '/chat/{tradeId}/messages';
  static const String chatRead = '/chat/{tradeId}/read';
  static const String chatUnreadCount = '/chat/unread-count';

  // Admin endpoints
  static const String adminDashboard = '/admin/dashboard';
  static const String adminUsers = '/admin/users';
  static const String adminTrades = '/admin/trades';
  static const String adminDisputes = '/admin/disputes';

  // Health endpoints
  static const String health = '/health';
  static const String healthDetailed = '/health/detailed';

  // Metrics endpoints
  static const String metrics = '/metrics';
  static const String metricsSystem = '/metrics/system';

  // File upload limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/webp'
  ];
  static const List<String> allowedDocumentTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png'
  ];

  // Pagination defaults
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Request timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // Rate limiting
  static const int maxRequestsPerMinute = 60;
  static const Duration rateLimitWindow = Duration(minutes: 1);

  // WebSocket events
  static const String wsConnect = 'connect';
  static const String wsDisconnect = 'disconnect';
  static const String wsJoinTrade = 'join_trade';
  static const String wsLeaveTrade = 'leave_trade';
  static const String wsSendMessage = 'send_message';
  static const String wsNewMessage = 'new_message';
  static const String wsTypingStart = 'typing_start';
  static const String wsTypingStop = 'typing_stop';
  static const String wsUserTyping = 'user_typing';
  static const String wsUserStoppedTyping = 'user_stopped_typing';
  static const String wsTradeUpdate = 'trade_update';
  static const String wsNotification = 'notification';
  static const String wsRateLimitExceeded = 'rate_limit_exceeded';
  static const String wsError = 'error';

  // Supported cryptocurrencies
  static const List<String> supportedCryptos = [
    'USDT',
    'USDC', 
    'DAI',
    'BTC',
    'ETH'
  ];

  // Supported networks
  static const List<String> supportedNetworks = [
    'polygon',
    'ethereum',
    'bitcoin'
  ];

  // Supported fiat currencies
  static const List<String> supportedFiatCurrencies = [
    'KES', // Kenyan Shilling
    'TZS', // Tanzanian Shilling
    'UGX', // Ugandan Shilling
    'RWF', // Rwandan Franc
    'USD'  // US Dollar
  ];

  // Supported countries
  static const List<String> supportedCountries = [
    'KE', // Kenya
    'TZ', // Tanzania
    'UG', // Uganda
    'RW'  // Rwanda
  ];

  // Payment methods
  static const List<String> paymentMethods = [
    'bank_transfer',
    'mobile_money',
    'cash',
    'other'
  ];

  // Mobile money providers
  static const List<String> mobileMoneyProviders = [
    'M-Pesa',
    'Airtel Money',
    'Tigo Pesa',
    'MTN Mobile Money'
  ];

  // Trade statuses
  static const List<String> tradeStatuses = [
    'created',
    'funded',
    'payment_sent',
    'payment_confirmed',
    'completed',
    'disputed',
    'cancelled',
    'refunded',
    'expired'
  ];

  // Dispute categories
  static const List<String> disputeCategories = [
    'payment_not_received',
    'payment_not_sent',
    'wrong_amount',
    'fake_payment_proof',
    'account_issues',
    'communication_issues',
    'other'
  ];

  // User verification levels
  static const List<String> verificationLevels = [
    'unverified',
    'email_verified',
    'phone_verified',
    'identity_verified',
    'fully_verified'
  ];

  // Commission rates
  static const double defaultCommissionRate = 0.005; // 0.5%
  static const double maxCommissionRate = 0.1; // 10%

  // Trade limits
  static const double minTradeAmount = 10.0; // USD equivalent
  static const double maxTradeAmount = 50000.0; // USD equivalent
  static const Duration tradeTimeout = Duration(hours: 24);
  static const Duration disputeTimeout = Duration(days: 7);

  // Reputation system
  static const int minReputationScore = 0;
  static const int maxReputationScore = 100;
  static const int defaultReputationScore = 0;

  // Helper methods
  static String replacePathParams(String path, Map<String, String> params) {
    String result = path;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value);
    });
    return result;
  }

  static bool isValidCrypto(String crypto) {
    return supportedCryptos.contains(crypto.toUpperCase());
  }

  static bool isValidNetwork(String network) {
    return supportedNetworks.contains(network.toLowerCase());
  }

  static bool isValidFiatCurrency(String currency) {
    return supportedFiatCurrencies.contains(currency.toUpperCase());
  }

  static bool isValidCountry(String country) {
    return supportedCountries.contains(country.toUpperCase());
  }

  static bool isValidPaymentMethod(String method) {
    return paymentMethods.contains(method.toLowerCase());
  }
}
