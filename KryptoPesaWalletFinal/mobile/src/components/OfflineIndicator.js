/**
 * Offline Indicator Component
 * Shows network status and queued operations
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Chip, Text, IconButton, Portal, Modal } from 'react-native-paper';
import { useSelector } from 'react-redux';
import { useTheme } from 'react-native-paper';
import offlineManager from '../services/offlineManager';

const OfflineIndicator = () => {
  const theme = useTheme();
  const networkStatus = useSelector(state => state.app.networkStatus);
  const [queuedOperations, setQueuedOperations] = useState(0);
  const [showDetails, setShowDetails] = useState(false);
  const [syncInProgress, setSyncInProgress] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Listen for offline manager updates
    const updateStatus = () => {
      const status = offlineManager.getStatus();
      setQueuedOperations(status.queuedOperations);
      setSyncInProgress(status.syncInProgress);
    };

    // Initial status
    updateStatus();

    // Listen for connectivity changes
    const connectivityListener = (isOnline, wasOnline) => {
      updateStatus();
      
      if (!wasOnline && isOnline) {
        // Show sync animation
        showSyncAnimation();
      }
    };

    offlineManager.addListener(connectivityListener);

    // Update status periodically
    const interval = setInterval(updateStatus, 5000);

    return () => {
      offlineManager.removeListener(connectivityListener);
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    // Show/hide indicator based on network status
    if (networkStatus === 'offline' || queuedOperations > 0) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [networkStatus, queuedOperations, fadeAnim]);

  const showSyncAnimation = () => {
    // Pulse animation for sync
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const getIndicatorConfig = () => {
    if (syncInProgress) {
      return {
        icon: 'sync',
        text: 'Syncing...',
        color: theme.colors.primary,
        backgroundColor: theme.colors.primaryContainer,
        textColor: theme.colors.onPrimaryContainer,
      };
    }

    if (networkStatus === 'offline') {
      return {
        icon: 'wifi-off',
        text: queuedOperations > 0 ? `Offline (${queuedOperations} queued)` : 'Offline',
        color: theme.colors.error,
        backgroundColor: theme.colors.errorContainer,
        textColor: theme.colors.onErrorContainer,
      };
    }

    if (queuedOperations > 0) {
      return {
        icon: 'cloud-upload',
        text: `${queuedOperations} pending`,
        color: theme.colors.tertiary,
        backgroundColor: theme.colors.tertiaryContainer,
        textColor: theme.colors.onTertiaryContainer,
      };
    }

    return null;
  };

  const config = getIndicatorConfig();

  if (!config) {
    return null;
  }

  return (
    <>
      <Animated.View 
        style={[
          styles.container,
          { opacity: fadeAnim }
        ]}
      >
        <Chip
          icon={config.icon}
          style={[
            styles.chip,
            { backgroundColor: config.backgroundColor }
          ]}
          textStyle={[
            styles.chipText,
            { color: config.textColor }
          ]}
          onPress={() => setShowDetails(true)}
        >
          {config.text}
        </Chip>
      </Animated.View>

      <Portal>
        <Modal
          visible={showDetails}
          onDismiss={() => setShowDetails(false)}
          contentContainerStyle={[
            styles.modal,
            { backgroundColor: theme.colors.surface }
          ]}
        >
          <OfflineDetailsModal
            onClose={() => setShowDetails(false)}
            networkStatus={networkStatus}
            queuedOperations={queuedOperations}
            syncInProgress={syncInProgress}
          />
        </Modal>
      </Portal>
    </>
  );
};

const OfflineDetailsModal = ({ 
  onClose, 
  networkStatus, 
  queuedOperations, 
  syncInProgress 
}) => {
  const theme = useTheme();
  const [operations, setOperations] = useState([]);

  useEffect(() => {
    // Get queued operations details
    const status = offlineManager.getStatus();
    setOperations(offlineManager.operationQueue || []);
  }, []);

  const handleClearQueue = async () => {
    await offlineManager.clearQueue();
    setOperations([]);
    onClose();
  };

  const handleForceSync = async () => {
    if (networkStatus === 'online') {
      await offlineManager.startBackgroundSync();
    }
  };

  return (
    <View style={styles.modalContent}>
      <View style={styles.modalHeader}>
        <Text variant="headlineSmall" style={styles.modalTitle}>
          Connection Status
        </Text>
        <IconButton
          icon="close"
          onPress={onClose}
          style={styles.closeButton}
        />
      </View>

      <View style={styles.statusSection}>
        <View style={styles.statusRow}>
          <Text variant="bodyLarge" style={styles.statusLabel}>
            Network:
          </Text>
          <Chip
            icon={networkStatus === 'online' ? 'wifi' : 'wifi-off'}
            style={[
              styles.statusChip,
              {
                backgroundColor: networkStatus === 'online' 
                  ? theme.colors.primaryContainer 
                  : theme.colors.errorContainer
              }
            ]}
            textStyle={{
              color: networkStatus === 'online'
                ? theme.colors.onPrimaryContainer
                : theme.colors.onErrorContainer
            }}
          >
            {networkStatus === 'online' ? 'Online' : 'Offline'}
          </Chip>
        </View>

        <View style={styles.statusRow}>
          <Text variant="bodyLarge" style={styles.statusLabel}>
            Queued Operations:
          </Text>
          <Text variant="bodyLarge" style={styles.statusValue}>
            {queuedOperations}
          </Text>
        </View>

        <View style={styles.statusRow}>
          <Text variant="bodyLarge" style={styles.statusLabel}>
            Sync Status:
          </Text>
          <Text variant="bodyLarge" style={styles.statusValue}>
            {syncInProgress ? 'In Progress' : 'Idle'}
          </Text>
        </View>
      </View>

      {operations.length > 0 && (
        <View style={styles.operationsSection}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Queued Operations
          </Text>
          {operations.slice(0, 5).map((operation, index) => (
            <View key={operation.id} style={styles.operationItem}>
              <Text variant="bodyMedium" style={styles.operationType}>
                {operation.type}
              </Text>
              <Text variant="bodySmall" style={styles.operationTime}>
                {new Date(operation.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          ))}
          {operations.length > 5 && (
            <Text variant="bodySmall" style={styles.moreOperations}>
              +{operations.length - 5} more operations
            </Text>
          )}
        </View>
      )}

      <View style={styles.actionButtons}>
        {networkStatus === 'online' && queuedOperations > 0 && (
          <Chip
            icon="sync"
            onPress={handleForceSync}
            style={[styles.actionChip, { backgroundColor: theme.colors.primaryContainer }]}
            textStyle={{ color: theme.colors.onPrimaryContainer }}
          >
            Force Sync
          </Chip>
        )}
        
        {queuedOperations > 0 && (
          <Chip
            icon="delete"
            onPress={handleClearQueue}
            style={[styles.actionChip, { backgroundColor: theme.colors.errorContainer }]}
            textStyle={{ color: theme.colors.onErrorContainer }}
          >
            Clear Queue
          </Chip>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    zIndex: 1000,
    alignItems: 'center',
  },
  chip: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  modal: {
    margin: 20,
    borderRadius: 12,
    padding: 0,
    elevation: 8,
  },
  modalContent: {
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontWeight: 'bold',
  },
  closeButton: {
    margin: 0,
  },
  statusSection: {
    marginBottom: 20,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusLabel: {
    fontWeight: '500',
  },
  statusValue: {
    fontWeight: 'normal',
  },
  statusChip: {
    height: 28,
  },
  operationsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 12,
  },
  operationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    marginBottom: 8,
  },
  operationType: {
    fontWeight: '500',
    flex: 1,
  },
  operationTime: {
    opacity: 0.7,
  },
  moreOperations: {
    textAlign: 'center',
    fontStyle: 'italic',
    opacity: 0.7,
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionChip: {
    minWidth: 120,
  },
});

export default OfflineIndicator;
