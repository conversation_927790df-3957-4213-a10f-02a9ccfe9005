/**
 * Offline Manager Service
 * Handles offline functionality, operation queuing, and background sync
 */

import NetInfo from '@react-native-community/netinfo';
import EncryptedStorage from 'react-native-encrypted-storage';
import { store } from '../store';
import { setNetworkStatus } from '../store/slices/appSlice';
import { showMessage } from 'react-native-flash-message';

class OfflineManager {
  constructor() {
    this.isOnline = true;
    this.operationQueue = [];
    this.syncInProgress = false;
    this.listeners = [];
    this.retryAttempts = new Map();
    this.maxRetryAttempts = 3;
    this.retryDelay = 5000; // 5 seconds
    
    // Storage keys
    this.QUEUE_STORAGE_KEY = 'offline_operation_queue';
    this.CACHE_STORAGE_KEY = 'offline_data_cache';
    this.SYNC_STATUS_KEY = 'last_sync_status';
  }

  /**
   * Initialize offline manager
   */
  async initialize() {
    try {
      // Load persisted queue
      await this.loadPersistedQueue();
      
      // Set up network monitoring
      this.setupNetworkMonitoring();
      
      // Load cached data
      await this.loadCachedData();
      
      console.log('OfflineManager initialized');
    } catch (error) {
      console.error('Failed to initialize OfflineManager:', error);
    }
  }

  /**
   * Set up network connectivity monitoring
   */
  setupNetworkMonitoring() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected && state.isInternetReachable;
      
      // Update Redux store
      store.dispatch(setNetworkStatus(this.isOnline ? 'online' : 'offline'));
      
      // Notify listeners
      this.notifyListeners(this.isOnline, wasOnline);
      
      // Handle connectivity changes
      if (!wasOnline && this.isOnline) {
        this.handleOnlineTransition();
      } else if (wasOnline && !this.isOnline) {
        this.handleOfflineTransition();
      }
    });
  }

  /**
   * Handle transition from offline to online
   */
  async handleOnlineTransition() {
    console.log('Device came online - starting sync');
    
    showMessage({
      message: 'Connection restored',
      description: 'Syncing your data...',
      type: 'success',
      duration: 3000,
    });

    // Start background sync
    await this.startBackgroundSync();
  }

  /**
   * Handle transition from online to offline
   */
  handleOfflineTransition() {
    console.log('Device went offline');
    
    showMessage({
      message: 'No internet connection',
      description: 'You can still view cached data and queue operations',
      type: 'warning',
      duration: 5000,
    });
  }

  /**
   * Queue operation for later execution
   */
  async queueOperation(operation) {
    const queuedOperation = {
      id: this.generateOperationId(),
      ...operation,
      timestamp: Date.now(),
      retryCount: 0,
      status: 'pending'
    };

    this.operationQueue.push(queuedOperation);
    await this.persistQueue();

    console.log(`Operation queued: ${operation.type}`, queuedOperation.id);
    
    return queuedOperation.id;
  }

  /**
   * Execute operation with offline handling
   */
  async executeOperation(operation, fallbackData = null) {
    if (this.isOnline) {
      try {
        return await operation.execute();
      } catch (error) {
        if (this.isNetworkError(error)) {
          // Queue for retry when online
          await this.queueOperation({
            type: operation.type,
            execute: operation.execute,
            fallbackData,
            originalError: error.message
          });
          
          if (fallbackData) {
            return fallbackData;
          }
          
          throw new Error('Operation queued for retry when online');
        }
        throw error;
      }
    } else {
      // Offline - queue operation
      await this.queueOperation({
        type: operation.type,
        execute: operation.execute,
        fallbackData
      });
      
      if (fallbackData) {
        return fallbackData;
      }
      
      throw new Error('Operation queued - device is offline');
    }
  }

  /**
   * Start background sync process
   */
  async startBackgroundSync() {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    this.syncInProgress = true;

    try {
      console.log(`Starting sync of ${this.operationQueue.length} operations`);
      
      // Process queued operations
      await this.processQueue();
      
      // Sync critical data
      await this.syncCriticalData();
      
      // Update last sync timestamp
      await EncryptedStorage.setItem(this.SYNC_STATUS_KEY, JSON.stringify({
        lastSync: Date.now(),
        status: 'success'
      }));

      console.log('Background sync completed successfully');
      
    } catch (error) {
      console.error('Background sync failed:', error);
      
      await EncryptedStorage.setItem(this.SYNC_STATUS_KEY, JSON.stringify({
        lastSync: Date.now(),
        status: 'failed',
        error: error.message
      }));
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Process queued operations
   */
  async processQueue() {
    const pendingOperations = this.operationQueue.filter(op => op.status === 'pending');
    
    for (const operation of pendingOperations) {
      try {
        console.log(`Processing queued operation: ${operation.type}`);
        
        const result = await operation.execute();
        
        // Mark as completed
        operation.status = 'completed';
        operation.completedAt = Date.now();
        operation.result = result;
        
        console.log(`Operation completed: ${operation.id}`);
        
      } catch (error) {
        console.error(`Operation failed: ${operation.id}`, error);
        
        operation.retryCount++;
        operation.lastError = error.message;
        
        if (operation.retryCount >= this.maxRetryAttempts) {
          operation.status = 'failed';
          operation.failedAt = Date.now();
        } else {
          // Schedule retry
          setTimeout(() => {
            this.retryOperation(operation);
          }, this.retryDelay * operation.retryCount);
        }
      }
    }

    // Remove completed operations
    this.operationQueue = this.operationQueue.filter(op => 
      op.status !== 'completed' && op.status !== 'failed'
    );
    
    await this.persistQueue();
  }

  /**
   * Retry failed operation
   */
  async retryOperation(operation) {
    if (!this.isOnline) {
      return;
    }

    try {
      console.log(`Retrying operation: ${operation.id} (attempt ${operation.retryCount + 1})`);
      
      const result = await operation.execute();
      
      operation.status = 'completed';
      operation.completedAt = Date.now();
      operation.result = result;
      
      await this.persistQueue();
      
    } catch (error) {
      console.error(`Retry failed for operation: ${operation.id}`, error);
      
      operation.retryCount++;
      operation.lastError = error.message;
      
      if (operation.retryCount >= this.maxRetryAttempts) {
        operation.status = 'failed';
        operation.failedAt = Date.now();
        await this.persistQueue();
      }
    }
  }

  /**
   * Sync critical data (trades, offers, wallet balance)
   */
  async syncCriticalData() {
    try {
      const { apiService } = require('./apiService');
      
      // Sync user trades
      const trades = await apiService.trades.getAll();
      await this.cacheData('trades', trades.data);
      
      // Sync active offers
      const offers = await apiService.offers.getAll({ status: 'active' });
      await this.cacheData('offers', offers.data);
      
      // Sync wallet data
      const wallet = await apiService.wallet.getBalance();
      await this.cacheData('wallet', wallet.data);
      
      console.log('Critical data sync completed');
      
    } catch (error) {
      console.error('Critical data sync failed:', error);
    }
  }

  /**
   * Cache data for offline access
   */
  async cacheData(key, data) {
    try {
      const cache = await this.getCachedData();
      cache[key] = {
        data,
        timestamp: Date.now(),
        ttl: this.getCacheTTL(key)
      };
      
      await EncryptedStorage.setItem(this.CACHE_STORAGE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.error(`Failed to cache data for key: ${key}`, error);
    }
  }

  /**
   * Get cached data
   */
  async getCachedData(key = null) {
    try {
      const cacheString = await EncryptedStorage.getItem(this.CACHE_STORAGE_KEY);
      const cache = cacheString ? JSON.parse(cacheString) : {};
      
      if (key) {
        const item = cache[key];
        if (item && Date.now() - item.timestamp < item.ttl) {
          return item.data;
        }
        return null;
      }
      
      return cache;
    } catch (error) {
      console.error('Failed to get cached data:', error);
      return key ? null : {};
    }
  }

  /**
   * Get cache TTL for different data types
   */
  getCacheTTL(key) {
    const ttlMap = {
      trades: 5 * 60 * 1000,    // 5 minutes
      offers: 2 * 60 * 1000,    // 2 minutes
      wallet: 1 * 60 * 1000,    // 1 minute
      user: 10 * 60 * 1000,     // 10 minutes
      default: 5 * 60 * 1000    // 5 minutes
    };
    
    return ttlMap[key] || ttlMap.default;
  }

  /**
   * Load persisted operation queue
   */
  async loadPersistedQueue() {
    try {
      const queueString = await EncryptedStorage.getItem(this.QUEUE_STORAGE_KEY);
      if (queueString) {
        this.operationQueue = JSON.parse(queueString);
        console.log(`Loaded ${this.operationQueue.length} persisted operations`);
      }
    } catch (error) {
      console.error('Failed to load persisted queue:', error);
      this.operationQueue = [];
    }
  }

  /**
   * Persist operation queue
   */
  async persistQueue() {
    try {
      await EncryptedStorage.setItem(this.QUEUE_STORAGE_KEY, JSON.stringify(this.operationQueue));
    } catch (error) {
      console.error('Failed to persist queue:', error);
    }
  }

  /**
   * Load cached data on initialization
   */
  async loadCachedData() {
    try {
      const cache = await this.getCachedData();
      console.log(`Loaded cached data for ${Object.keys(cache).length} keys`);
    } catch (error) {
      console.error('Failed to load cached data:', error);
    }
  }

  /**
   * Check if error is network-related
   */
  isNetworkError(error) {
    const networkErrorMessages = [
      'Network Error',
      'timeout',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ECONNRESET'
    ];
    
    return networkErrorMessages.some(msg => 
      error.message.toLowerCase().includes(msg.toLowerCase())
    );
  }

  /**
   * Generate unique operation ID
   */
  generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add connectivity listener
   */
  addListener(listener) {
    this.listeners.push(listener);
  }

  /**
   * Remove connectivity listener
   */
  removeListener(listener) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  /**
   * Notify all listeners of connectivity changes
   */
  notifyListeners(isOnline, wasOnline) {
    this.listeners.forEach(listener => {
      try {
        listener(isOnline, wasOnline);
      } catch (error) {
        console.error('Error in connectivity listener:', error);
      }
    });
  }

  /**
   * Get offline status
   */
  getStatus() {
    return {
      isOnline: this.isOnline,
      queuedOperations: this.operationQueue.length,
      syncInProgress: this.syncInProgress,
      lastSync: null // Would load from storage
    };
  }

  /**
   * Clear all cached data
   */
  async clearCache() {
    try {
      await EncryptedStorage.removeItem(this.CACHE_STORAGE_KEY);
      console.log('Cache cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  /**
   * Clear operation queue
   */
  async clearQueue() {
    try {
      this.operationQueue = [];
      await EncryptedStorage.removeItem(this.QUEUE_STORAGE_KEY);
      console.log('Operation queue cleared');
    } catch (error) {
      console.error('Failed to clear queue:', error);
    }
  }
}

// Create singleton instance
const offlineManager = new OfflineManager();

export default offlineManager;
